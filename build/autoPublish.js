// eslint-disable-next-line @typescript-eslint/no-var-requires
const axios = require('axios');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const fs = require('fs-extra');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const path = require('path');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const AdmZip = require('adm-zip');

/**
 * 下载并解压缩文件
 * @param {string} url 下载文件的URL
 * @param {string} destFolder 解压到指定文件夹
 */
const downloadAndExtract = async (url, destFolder) => {
  try {
    // 检查并删除现有目录
    if (await fs.pathExists(destFolder)) {
      await fs.remove(destFolder);
      console.log('已删除 downloads 目录');
    }

    // 创建目标文件夹
    await fs.ensureDir(destFolder);

    // 定义下载路径和文件名
    const zipPath = path.join(destFolder, 'downloaded.zip');

    // 下载文件
    const writer = fs.createWriteStream(zipPath);
    const response = await axios({
      url,
      method: 'GET',
      responseType: 'stream'
    });

    response.data.pipe(writer);

    // 等待文件下载完成
    await new Promise((resolve, reject) => {
      writer.on('finish', resolve);
      writer.on('error', reject);
    });

    // 解压文件
    const zip = new AdmZip(zipPath);
    zip.extractAllTo(destFolder, true);
    console.log('文件已解压缩至', destFolder);

    const downloadDistFolder = path.join(destFolder, 'dist');
    const projectRoot = process.cwd();
    const projectDistFolder = path.join(projectRoot, 'dist');

    // 删除下载的压缩文件
    await fs.remove(zipPath);
    console.log('已删除下载的压缩文件 downloaded.zip');

    // 检查并删除 __MACOSX 目录
    const macosxPath = path.join(destFolder, '__MACOSX');
    if (await fs.pathExists(macosxPath)) {
      await fs.remove(macosxPath);
      console.log('已删除 __MACOSX 目录');
    }

    // 删除 index.html 文件
    const indexPath = path.join(downloadDistFolder, 'index.html');
    if (await fs.pathExists(indexPath)) {
      await fs.remove(indexPath);
      console.log('已删除 index.html 文件');
    }

    // 删除 css 文件夹下的 courseware.css
    const coursewareCssPath = path.join(downloadDistFolder, 'static', 'css', 'courseware.css');
    if (await fs.pathExists(coursewareCssPath)) {
      await fs.remove(coursewareCssPath);
      console.log('已删除 courseware.css 文件');
    }

    // 保留的 js 文件
    const keepJsFiles = ['chunk-vendors.4a258a4c41.js', 'fillbanks.715fa10db3.js', 'game.c1c5033350.js'];
    const jsFolderPath = path.join(downloadDistFolder, 'static', 'js');

    const jsFiles = await fs.readdir(jsFolderPath);
    for (const file of jsFiles) {
      if (!keepJsFiles.includes(file)) {
        await fs.remove(path.join(jsFolderPath, file));
        console.log(`已删除 ${file} 文件`);
      }
    }

    // 从和 downloads 同级目录下的 dist 文件夹进行文件拷贝
    const sourceIndex = path.join(projectDistFolder, 'index.html');
    const destIndex = path.join(downloadDistFolder, 'index.html');
    await fs.copy(sourceIndex, destIndex);
    console.log('已拷贝 index.html 文件到 downloads/dist 文件夹下');

    const sourceCssFolder = path.join(projectDistFolder, 'static', 'css');
    const destCssFolder = path.join(downloadDistFolder, 'static', 'css');
    await fs.copy(sourceCssFolder, destCssFolder, { overwrite: true });
    console.log('已拷贝 css 文件夹内容到 downloads/dist/static/css 文件夹下');

    const sourceJsFolder = path.join(projectDistFolder, 'static', 'js');
    const destJsFolder = path.join(downloadDistFolder, 'static', 'js');
    await fs.copy(sourceJsFolder, destJsFolder, { overwrite: true });
    console.log('已拷贝 js 文件夹内容到 downloads/dist/static/js 文件夹下');

    console.log('下载并解压操作成功完成');
  } catch (err) {
    console.error('操作失败:', err);
  }
};

/**
 * 压缩文件
 * @param {string} destFolder 目标文件夹路径
 * @returns {string} 压缩后的文件路径
 */
const fileToZip = (destFolder) => {
  // 获取当前日期和时间
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');  // 月份从0开始，所以需要+1
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const fileName = `${year}${month}${day}_${hours}${minutes}${seconds}`
  const zipFileName = `dist_${fileName}.zip`;
  const downloadDistFolder = path.join(destFolder, 'dist');

  // 压缩 dist 文件夹
  const zipOutputPath = path.join(destFolder, zipFileName);
  const distZip = new AdmZip();
  distZip.addLocalFolder(downloadDistFolder, 'dist');
  distZip.writeZip(zipOutputPath);

  console.log(`已将 dist 文件夹压缩成 ${zipFileName}`);
  return { zipFilePath: zipOutputPath, fileName };
};

// 导出这些函数以便在其他文件中使用
module.exports = {
  downloadAndExtract,
  fileToZip
};
