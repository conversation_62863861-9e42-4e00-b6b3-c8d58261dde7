// upload.js

// eslint-disable-next-line @typescript-eslint/no-var-requires
const path = require('path');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const fs = require('fs-extra');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const AWS = require('aws-sdk');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const OSS = require('ali-oss');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const crypto = require('crypto');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const axios = require('axios');

// eslint-disable-next-line @typescript-eslint/no-var-requires
const { downloadAndExtract, fileToZip } = require('./autoPublish');

// 阿里云 OSS 配置
const ossClient = new OSS({
  region: 'oss-cn-beijing',
  accessKeyId: 'LTAI5tENKmAPUDWNM32M85KF',
  accessKeySecret: '******************************',
  bucket: 'pa-oss-cn-prod'
});
// 配置 AWS S3 凭证（推荐使用环境变量或配置文件）
AWS.config.update({
  accessKeyId: '********************',
  secretAccessKey: 'Eq8XWHCEf/ESUs/WGDBtuapkIKPLLMPn0feR2rVN',
  region: 'us-west-2',
});
const s3 = new AWS.S3({ apiVersion: '2006-03-01' });

/**
 * 上传文件到 AWS S3
 * @param {string} filePath 本地文件路径
 * @param {string} bucketName S3桶名称
 * @param {string} key S3中的文件路径
 */
const uploadToS3 = async (filePath, bucketName, key) => {
  // 根据文件扩展名设置 Content-Type
  const extname = path.extname(filePath);
  let contentType = 'application/octet-stream'; // 默认值

  switch (extname) {
    case '.html':
      contentType = 'text/html';
      break;
    case '.css':
      contentType = 'text/css';
      break;
    case '.js':
      contentType = 'application/javascript';
      break;
    case '.jpeg':
    case '.jpg':
      contentType = 'image/jpeg';
      break;
    case '.png':
      contentType = 'image/png';
      break;
    case '.gif':
      contentType = 'image/gif';
      break;
    case '.bmp':
      contentType = 'image/bmp';
      break;
    case '.svg':
      contentType = 'image/svg+xml';
      break;
    case '.webp':
      contentType = 'image/webp';
      break;
    // 添加其他类型（如字体文件、视频文件等），根据需求扩展
    default:
      // 保持默认值
      break;
  }

  try {
    const fileContent = await fs.readFile(filePath);
    const params = {
      Bucket: bucketName,
      Key: key,
      Body: fileContent,
    };
    if (extname !== '.zip') {
      params.ContentType = contentType;
    }
    const data = await s3.upload(params).promise();
    console.log(`文件已上传至 S3: ${data.Location}`);
  } catch (err) {
    console.error('上传到 S3 失败:', err);
  }
}

/**
 * 上传文件到阿里云 OSS
 * @param {string} filePath 本地文件路径
 * @param {string} key OSS中的文件路径
 */
const uploadToOSS = async (filePath, key) => {
  try {
    const result = await ossClient.put(key, filePath);
    console.log(`文件已上传至 OSS: ${result.url}`);
  } catch (err) {
    console.error('上传到 OSS 失败:', err);
  }
};

/**
 * 递归上传目录到 AWS S3
 * @param {string} localDir 本地目录路径
 * @param {string} bucketName S3桶名称
 * @param {string} remoteDir S3中的目录路径
 */
const uploadDirToS3 = async (localDir, bucketName, remoteDir) => {
  const files = await fs.readdir(localDir);
  for (const file of files) {
    const localFilePath = path.join(localDir, file);
    const remoteFilePath = path.join(remoteDir, file);
    const stats = await fs.stat(localFilePath);
    if (stats.isDirectory()) {
      await uploadDirToS3(localFilePath, bucketName, remoteFilePath);
    } else {
      await uploadToS3(localFilePath, bucketName, remoteFilePath);
    }
  }
};

/**
 * 计算文件的 MD5
 * @param {string} filePath 文件路径
 * @returns {Promise<string>} 文件的 MD5 校验值
 */
const calculateMD5 = async (filePath) => {
  return new Promise((resolve, reject) => {
    const hash = crypto.createHash('md5');
    const stream = fs.createReadStream(filePath);
    stream.on('data', (data) => hash.update(data));
    stream.on('end', () => resolve(hash.digest('hex')));
    stream.on('error', reject);
  });
};

/**
 * 计算文件的大小
 * @param {string} filePath 文件路径
 * @returns {Promise<number>} 文件的大小（字节）
 */
const calculateFileSize = async (filePath) => {
  const stats = await fs.stat(filePath);
  return stats.size;
};

/**
  * 获取Markdown配置
  */
const getMarkdownConfig = (md5, fileSize, fileName) => {
  const zipName = `dist_${fileName}.zip`
  const minVersion = process.env.MIN_VERSION || '3.4.0'
  const json = {
    "url": `https://download-pa-s3.thethinkacademy.com/mobile/courseware/bridge/${zipName}`,
    "onlineUrl": `https://download-pa-s3.thethinkacademy.com/mobile/courseware/beta/${fileName}/`,
    "urlSpareList": [
      `https://pa-oss-cn-prod.thethinkacademy.com/courseware/bridge/${zipName}`
    ],
    "zipMd5": md5,
    "zipSize": fileSize,
    "minVersion": minVersion
  }
  const config = {
    title: `课件桥阶层-发布通知`,
    text: `\n\n**课件桥阶层-发布通知**\n\njson字符串:${JSON.stringify(json)}`,
  }
  return config
}
/**
  * 生成签名
  * @param {*} timestamp
  * @returns
  */
const generateSign = (timestamp) => {
  console.log('timestamp', robot.secret)
  let str = `${timestamp}\n${robot.secret}`
  let hash = crypto
    .createHmac('SHA256', robot.secret)
    .update(str, 'utf8')
    .digest('base64')

  hash = encodeURIComponent(hash)
  return hash
}

const sendData = async (data) => {
  try {
    let timestamp = +new Date()
    let sign = generateSign(timestamp)
    let url = `${robot.url}?access_token=${robot.token}&timestamp=${timestamp}&sign=${sign}`
    let result = await axios.post(url, data)
    return result.data
  } catch (error) {
    console.error('发送群消息失败', error)
  }
}
// 使用该函数
const url = 'https://download-pa-s3.thethinkacademy.com/mobile/courseware/bridge/dist_20240924001.zip';
const destFolder = './downloads';
const robot = {
  url: 'https://yach-oapi.zhiyinlou.com/robot/send',
  token: 'eTNac1FsUVM1QVAzMmhuNDFBSnNnNW1LM0Q0M3h1TmEra0VMb0dDcWlxWHlQK2R3Tmp0MTRyOHl5MS9WVmRNLw',
  secret: 'SEC1dc3878d4ebfd1487395512d8ab9e428'
}

// 执行下载并解压缩
downloadAndExtract(url, destFolder).then(async () => {
  // 压缩文件
  const { zipFilePath, fileName } = fileToZip(destFolder);

  // 上传压缩文件到 AWS S3
  await uploadToS3(zipFilePath, 'pa-s3-prod', path.join('mobile/courseware/bridge', path.basename(zipFilePath)));

  // 上传压缩文件到阿里云 OSS
  await uploadToOSS(zipFilePath, `courseware/bridge/${path.basename(zipFilePath)}`);

  // 上传 downloads 下的 dist 文件夹到 AWS S3
  const distFolder = path.join(destFolder, 'dist');
  await uploadDirToS3(distFolder, 'pa-s3-prod', path.join('mobile/courseware/beta', fileName, 'dist'));


  // 计算 MD5 和文件大小
  const md5 = await calculateMD5(zipFilePath);
  const fileSize = await calculateFileSize(zipFilePath);

  console.log(`文件 MD5: ${md5}`);
  console.log(`文件大小: ${fileSize} 字节`)

  const markdownConfig = getMarkdownConfig(md5, fileSize, fileName);
  sendData({
    msgtype: 'markdown',
    markdown: markdownConfig
  }).then(res => {
    if (res.code != 200) {
      return console.log(`机器人群通知发送失败: ${res.msg}`)
    }
    console.log('机器人群通知发布成功!')
  })
}).catch(err => {
  console.error('整体操作失败:', err);
});
