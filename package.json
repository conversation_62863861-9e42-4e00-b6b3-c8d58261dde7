{"name": "@thinkacademy/courseware-player", "version": "10.4.5-beta", "main": "./lib/courseware-player.js", "module": "./lib/courseware-player.js", "scripts": {"start": "vue-cli-service serve --mode dev", "build": "vue-cli-service build --mode prod-mobile", "lib": "rimraf ./lib && rollup -c --environment NODE_ENV:production", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "pubzip": "node ./build/upload.js"}, "directories": {"lib": "lib", "test": "__tests__"}, "files": ["lib"], "dependencies": {"@sentry/browser": "^6.19.2", "@sentry/tracing": "^6.19.2", "axios": "^0.20.0", "lodash": "^4.17.20", "uuid": "^8.3.0"}, "devDependencies": {"@babel/plugin-proposal-object-rest-spread": "^7.11.0", "@microsoft/api-extractor": "^7.9.22", "@rollup/plugin-buble": "^0.21.3", "@rollup/plugin-commonjs": "^21.0.3", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-typescript": "^6.0.0", "@types/lodash": "^4.14.161", "@types/node": "^14.11.2", "@typescript-eslint/eslint-plugin": "^4.2.0", "@typescript-eslint/parser": "^4.2.0", "@vue/cli-plugin-babel": "^4.5.6", "@vue/cli-plugin-eslint": "^4.5.6", "@vue/cli-service": "^4.5.6", "@vue/compiler-sfc": "^3.0.0", "adm-zip": "^0.5.16", "ali-oss": "^6.21.0", "aws-sdk": "^2.1691.0", "core-js": "^3.6.5", "crypto": "^1.0.1", "eslint": "^7.10.0", "eslint-config-prettier": "^6.12.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-vue": "^6.2.2", "prettier": "^2.1.2", "regenerator-runtime": "^0.13.7", "rollup": "^2.70.1", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-terser": "^7.0.2", "runjs": "^4.4.2", "sass": "^1.26.11", "sass-loader": "^10.0.2", "style-loader": "^1.0.0", "ts-loader": "^8.0.4", "tslib": "^2.0.1", "typedoc": "^0.19.2", "typescript": "^4.0.3", "vue": "^3.0.0", "vue-template-compiler": "^2.6.12"}, "browserslist": ["safari >= 9", "chrome >= 78"], "publishConfig": {"access": "public", "registry": "https://npm.100tal.com/"}}