（1）乔接层参数拼接
（2）游戏预加载的进度日志上抛 ：游戏 id、页码、进度
（3）游戏预加载完成日志上抛：游戏 id、页码、完成标记
（4）老师发布游戏 kv 信令
（5）gameUrl 链接拼接日志
（6）面授课游戏预加载的进度日志上抛、完成日志上抛
（7）演示模式：游戏上抛事件信令上抛

### 地址

https://us-west-2.console.aws.amazon.com/s3/buckets/pa-s3-prod?prefix=mobile/courseware/&region=us-west-2&bucketType=general

https://oss.console.aliyun.com/bucket/oss-cn-beijing/pa-oss-cn-prod/object?path=courseware%2F

### 注意

移动端要用的压缩包要包含桥接层和填空题的文件,填空题如果没有更新,不需要重新打包,只需要把之前的压缩包里的桥接层的部分替换成新的,然后重新压缩上传

```js
curl --location --request POST 'http://biz.beta.thethinkacademy.com/master/masterConfig' \
--header 'Content-Type: application/json' \
--header 'Master-Token: J7qM9s5S8&@Kjhqn1DP!NSVB4n8VpOKW' \
--data-raw '{
    "token":"fJ7qM9s5S8&@Kjhqn1DP!NSVB4n8VpOKW",
    "configName":"h5BridgeConfig",
    "configValue":[
            {
            "url": "https://download-pa-s3.thethinkacademy.com/mobile/courseware/bridge/dist_20240618001.zip", // https://us-west-2.console.aws.amazon.com/s3/buckets/pa-s3-prod?prefix=mobile/courseware/bridge/&region=us-west-2&bucketType=general 只能新增,不要删除旧的,以防线上在用
            "onlineUrl": "https://download-pa-s3.thethinkacademy.com/mobile/courseware/", // https://us-west-2.console.aws.amazon.com/s3/buckets/pa-s3-prod?prefix=mobile/courseware/&region=us-west-2&bucketType=general
            "urlSpareList": [
                "https://pa-oss-cn-prod.thethinkacademy.com/courseware/bridge/dist_20240618001.zip" // https://oss.console.aliyun.com/bucket/oss-cn-beijing/pa-oss-cn-prod/object?path=courseware%2Fbridge%2F 只能新增,不能删除
            ],
            "zipMd5": "f8d3012432ebe324a9d2c0caae6ffda5", // md5 压缩包
            "zipSize": 1500341 // stat -f%z 压缩包
        }
    ]
}'
```

```js
curl --location --request POST 'http://biz.beta.thethinkacademy.com/master/masterConfig' \
--header 'Content-Type: application/json' \
--header 'Master-Token: J7qM9s5S8&@Kjhqn1DP!NSVB4n8VpOKW' \
--data-raw '{
    "token":"fJ7qM9s5S8&@Kjhqn1DP!NSVB4n8VpOKW",
    "configName":"h5BridgeConfig",
    "configValue":[
     {
        onlineUrl = "https://download-pa-s3.thethinkacademy.com/mobile/courseware/";
        url = "https://download-pa-s3.thethinkacademy.com/mobile/courseware/bridge/dist_20211208001.zip";
        urlSpareList =                     (
            "http://sszt-mgr.oss-accelerate.aliyuncs.com/ios/wikifile/111/dist_20211208001.zip"
        );
        zipMd5 = 684a6bd5e137896ba19242c4aecf7152;
        zipSize = 717026;
        "minVersion": "0.0.0"
    },
    {
        "url": "https://download-pa-s3.thethinkacademy.com/mobile/courseware/bridge/dist_20240827001.zip",
        "onlineUrl": "https://download-pa-s3.thethinkacademy.com/mobile/courseware/beta/20240827001/",
        "urlSpareList": [
            "https://pa-oss-cn-prod.thethinkacademy.com/courseware/bridge/dist_20240827001.zip"
        ],
        "zipMd5": "260897a42d1df0bd72975733a6a94918",
        "zipSize": 968226,
        "minVersion": "3.4.0"
    }
    ]
}'
```
