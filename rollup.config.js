import typescript from '@rollup/plugin-typescript';
import commonjs from '@rollup/plugin-commonjs';
import json from '@rollup/plugin-json';
import { terser } from 'rollup-plugin-terser';
import buble from '@rollup/plugin-buble';
import replace from 'rollup-plugin-replace';
import { version } from './package.json';
const env = process.env.NODE_ENV;
export default {
  input: './src/courseware-player.ts',
  output: [
    {
      dir: 'lib',
      format: 'cjs',
    },
  ],
  plugins: [
    typescript(),
    json(),
    terser({
      toplevel: true,
      output: {
        ascii_only: true,
      },
      compress: {
        pure_funcs: ['makeMap'],
      },
    }),
    buble({
      transforms: {
        asyncAwait: false,
        forOf: false,
      },
    }),
    commonjs(),
    replace({
      __VERSION__: version,
      __UPDATE_TIME__: +new Date(),
      'process.env.NODE_ENV': JSON.stringify(env),
    }),
  ],
  external: [
    'axios',
    'lodash',
    'uuid',
    'events',
    'lodash/throttle',
    'lodash/cloneDeep',
    'lodash/debounce',
  ],
};
