// 和教研云真实课件对接的消息定义

import { ItsEvent } from 'src/interface';

// courseware ->
export enum CoursewareEvent {
  Loaded = 'windowOnLoaded', // 课件加载完成
  CoursewareLoadError = 'coursewareLoadError',
  PageChanged = 'pageChanged', // 切换页码
  SendNeedChangePage = 'sendNeedChangePage', // 准备翻页的消息
  GetCatalogueInfo = 'getCatalogueInfo', // 获取目录
  GetBoundingClientRectByItemId = 'getBoundingClientRectByItemId',
  GetResourceInfoList = 'getResourceInfoList',
  TriggerMediaAction = 'triggerMediaAction',
  CoursewareLoadingProgress = 'coursewareLoadingProgress',
  TransferMessageSend = 'transferMessageSend',
  StoreCWState = 'storeCWState',
  CheckSourceAvailable = 'checkSourceAvailable',
  HyperlinksMessage = 'hyperlinksMessage',
  GetCWState = 'getCWState',
  Ready = 'cwIsReady',
  ReportLog = 'reportLog',
  AudioStateChanged = 'audioStateChanged',
  VideoStateChanged = 'videoStateChanged',
  MediaStateChanged = 'mediaStateChange',
  PageChangeToTeacher = 'pageChangeToTeacher',
  PageHasGame = 'pageHasGame',
  SendTestResult = 'sendTestResult',
}

// -> courseware
export enum CoursewareCommand {
  PageTurning = 'pageTurning',
  GetCatalogueInfo = 'getCatalogueInfo',
  GetResourceInfoList = 'getResourceInfoList',
  GetBoundingClientRectByItemId = 'getBoundingClientRectByItemId',
  RecoverCWState = 'recoverCWState',
  TransferMessageReceive = 'transferMessageReceive',
  SourceAvailable = 'sourceAvailable',
  SetCWOriginLocation = 'setCWOriginLocation',
  SetIsLocalCW = 'setIsLocalCW',
  AnimationChange = 'futureBlackboard',
  NoticeGetCWState = 'noticeGetCWState',
  SetUpBodyBackground = 'setUpBodyBackground',
  RecvIsMaster = 'recv_is_master',
  BeginTest = 'beginTest',
  EndTest = 'endTest',
  SetElementOperate = 'setElementOperate',
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface PageTurning {
  type: CoursewareCommand;
  page: number; // 翻到第几页
  speed: number; // 翻页速度
}

export interface TriggerMediaActionParam {
  currentPageNumber: number; // 3
  mediaType: string; // "video"
  action: string; // "play" "pause"
  elementId: string; // "video_7531255f682d50551989447a975e5cb8"
  elementUrl: string; // "resource/videos/08D5BB15F23A89A3801ADE8AC071691B.mp4"
  elementLeft: number; // 476.9757080078125
  elementTop: number; // 390.0879821777344
  elementWidth: number; // 448.33331298828125
  elementHeight: number; // 280.2083435058594
}

export interface QuestionResult {
  tabIndex?: string; // 是否需要区分标签页, 可选字段
  queId: string; // 试题ID
  type: string; // 题型
  userAnswerContent: string; // 多个空/多个选项答案用逗号分隔
  rightNum: number; // 答对空个数
  wrongNum: number; // 答错空个数
  unAnsweredNum: number; // 未作答个数
  isRight: number; // 0错误，1全对，2未作答
}

export interface GetBoundingClientRectByItemIdParam {
  elementId: string; // "video_7531255f682d50551989447a975e5cb8"
  elementLeft: number; // 476.9757080078125
  elementTop: number; // 390.0879821777344
  elementWidth: number; // 448.33331298828125
  elementHeight: number; // 280.2083435058594
}

export interface GetResourceInfoListParam {
  pageId: string;
  pageIndex: number;
  resources: {
    resourceId: string;
    type: string; // video, audio
    relativePath: string; // ./resource/video/1.mp4
    m3u8Path: string; // hls path
  }[];
}

export interface PageChangedParam {
  pageIndex: number;
  type: string;
  includeInteractiveTemplate: boolean;
  interactiveMode?: number;
}
export interface MediaLoadReportParam {
  id: string;
  loadTimes: number; //加载时间
  status: number; //1可用,0不可以
  type: string; //video/audio
  url: string;
}
export interface AudioStateChangedParam {
  changeType: string; //play,pause,ended
  curPageId: string;
  currentTime: number;
  duration: number;
  eId: string;
  muted: false;
  state: string; //pause,play
  type: string; //video,audio
  volume: number;
}
export interface VideoStateChangedParam extends AudioStateChangedParam {
  fullScreen: boolean;
  rate: number;
  src: string;
}
export enum CourseEnv {
  Production = 'prod', // 线上环境
  Prerelease = 'test', // 仿真环境
}

export interface WindowOnLoadedParam {
  title: string;
  env: CourseEnv;
}

export interface SetKjOriginLocationParam {
  cwId: string;
  url: string;
}

export type SourceAvailableParam = {
  id: string;
  url: string;
  available: boolean;
}[];

export type RecoverCWStateParam = unknown;
export type TransferMessageSendParam = unknown;
export type TransferMessageReceiveParam = unknown;

export interface CheckResourceAvailableItem {
  id: string;
  url: string;
}

export type CheckResourceAvailableParam = CheckResourceAvailableItem[];

export interface CatalogueItem {
  type: string; // item for normal page
  index: number; // for page turning
  hasIframe: false;
  main?: {
    type: any;
  }; // 页签/其他信息
  item: {
    id: string; // page id
    note: string; // 备注
    title: string; // 标题
    isHide: number; // 0 for normal ,1 for hide
    main?: {
      type: string;
      width?: string;
      height?: string;
    };
    itemsFile?: string; // json路径
    hasIframe: false;
    items?: gameItemInfo[];
  };
  isGame: boolean;
  isPrereload: number;
  supportKeepAndRestart: number;
  titleId: number;
  gameId: number; // 游戏id， item.gameId
  coursewareId: number; //
  gameIsNewPlatform: number;
}

export interface gameItemInfo {
  checkedResult: number;
  titleId: any;
  gameId: any;
  coursewareId: any;
  attr: {
    isPlay: any;
    isPrereload: any;
    url: any;
    align: 'left';
    bold: boolean;
    fill: string;
    fontname: string;
    fontsize: string;
    // ... add more properties if needed
  };
  className: string;
  content: string;
  extendPreAttr: boolean;
  id: string;
  name: string;
  posX: string;
  posY: string;
  questionPart: string;
  show: boolean;
  style: string;
  text2img: boolean;
  textFormatId: string;
  type: string;
  uid: string;
  zIndex: number;
  iframeType: string; // hwOnline
  // ... add more properties if needed
}
export interface gameItemInfo {
  checkedResult: number;
  titleId: any;
  gameId: any;
  coursewareId: any;
  attr: {
    isPlay: any;
    isPrereload: any;
    url: any;
    align: 'left';
    bold: boolean;
    fill: string;
    fontname: string;
    fontsize: string;
    // ... add more properties if needed
  };
  className: string;
  content: string;
  extendPreAttr: boolean;
  id: string;
  name: string;
  posX: string;
  posY: string;
  questionPart: string;
  show: boolean;
  style: string;
  text2img: boolean;
  textFormatId: string;
  type: string;
  uid: string;
  zIndex: number;
  iframeType: string; // hwOnline
  // ... add more properties if needed
}

export type GetCatalogueInfoParam = CatalogueItem[];

export interface HyperlinksMessageParam {
  targetPageId: string;
  targetPageIdx: number;
}

export interface StoreCWStateParam {
  key: string;
  value: unknown;
}

export enum NeedPageChangeDir {
  Next = 'next',
  Prev = 'previous',
}

export type ParamsToCW = {
  type: CoursewareCommand;
  data?: unknown;
  pageIndex?: string;
  requestMsg?: string;
  dir?: string;
  enforce?: boolean;
  curPageId?: string;
  interactId?: string;
  answerTip?: string;
  correctAnswer?: string;
};

export interface InitParams {
  gamePreload?: string;
  chapterId?: string;
  userId?: string;
  role?: string;
  client?: string;
  width?: number;
  height?: number;
  clientDescription?: string;
  // setDataPoint: (data: PointData) => void; // 埋点
  onEvent: (event: ItsEvent, data: unknown) => void; // 埋点
  // checkResource: FnCheckResource; // 埋点
  coursewareId: string;
}
