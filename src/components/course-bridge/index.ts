import { EventEmitter } from 'events';
import debounce from 'lodash/debounce';
import CallPromisify from './call-promisify';

import {
  GetBoundingClientRectByItemIdParam,
  CoursewareEvent,
  CoursewareCommand,
  GetResourceInfoListParam,
  TriggerMediaActionParam,
  QuestionResult,
  WindowOnLoadedParam,
  PageChangedParam,
  SourceAvailableParam,
  VideoStateChangedParam,
  AudioStateChangedParam,
  MediaLoadReportParam,
  ParamsToCW,
} from './course-type';

import { ItsError } from '../../interface';
import handleDataPoints from '../../libs/handle-data-points';
import { formatTime, isThinkAcademyPC } from '../../libs/utils';
export * from './course-type';

export default class CourseBridge extends EventEmitter {
  // static
  public static ELoadCanceled = 'E_LOAD_CANCELED';
  private static LOAD_TIMEOUT = 1000 * 60 * 10;
  private static MSG_TIMEOUT = 20000;
  // 翻页的动画可能会比较久，并有可能一直在进行，所以这里自己处理超时
  private static PAGE_TURNING_TIMEOUT = 100000;
  private static PAGE_TURNING_DBC_WAIT = 5000;
  private static __messageHandlerBinded: EventListener;
  private static __clickHandlerBinded: EventListener;
  private isFirstPageTurn = true;
  // core
  private callPromisify = new CallPromisify();
  private isGame = false;
  // page turning
  private lastPageIndex: number | string | null = null;
  private fnPageTurningDbc: () => void;
  public role = 'student';
  constructor(private iframe: HTMLIFrameElement) {
    super();
    window.removeEventListener('message', CourseBridge.__messageHandlerBinded);
    CourseBridge.__messageHandlerBinded = this.messageHandler.bind(this);
    if (
      isThinkAcademyPC &&
      this.role === 'student' &&
      document.getElementById('interactionController')
    ) {
      document
        .getElementById('interactionController')
        .removeEventListener('click', CourseBridge.__clickHandlerBinded);
      CourseBridge.__clickHandlerBinded = this.showGameDemoToast.bind(this);
    }
    this.fnPageTurningDbc = debounce(
      this.rejectPageTurning,
      CourseBridge.PAGE_TURNING_DBC_WAIT
    );
    this.addMessageListener();
  }

  load(src: string, role: string): Promise<WindowOnLoadedParam> {
    // 重新加载需要把pending的所有promise都立即reject，不然会触发timeout和时序问题
    this.callPromisify.rejectAll({ code: CourseBridge.ELoadCanceled });
    this.iframe.src = src;
    this.role = role;
    this.isFirstPageTurn = true;
    this.isGame = false;
    return this.callPromisify.record(
      CoursewareEvent.Loaded,
      CourseBridge.LOAD_TIMEOUT,
      { code: ItsError.LoadTimeout }
    );
  }

  getCatalogueInfo(): Promise<unknown> {
    const cmd = CoursewareCommand.GetCatalogueInfo;
    this.postMessage({ type: cmd });
    return this.callPromisify.record(cmd, CourseBridge.MSG_TIMEOUT, {
      code: ItsError.CatalogueInfo,
    });
  }

  getBoundingClientRectByItemId(
    id: string
  ): Promise<GetBoundingClientRectByItemIdParam> {
    this.postMessage({
      type: CoursewareCommand.GetBoundingClientRectByItemId,
      data: { itemId: id },
    });
    const callId = this.getCallId(
      CoursewareCommand.GetBoundingClientRectByItemId,
      id
    );
    return this.callPromisify.record(callId, CourseBridge.MSG_TIMEOUT, {
      code: ItsError.BoundingClientRectByItemId,
    });
  }

  getResourceInfoList(): Promise<GetResourceInfoListParam> {
    const cmd = CoursewareCommand.GetResourceInfoList;
    this.postMessage({ type: cmd });
    return this.callPromisify.record(cmd, CourseBridge.MSG_TIMEOUT, {
      code: ItsError.ResourceInfoList,
    });
  }

  // 如果翻页动作一直进行，在 PAGE_TURNING_TIMEOUT 时间内仍然没有做完，则会reject
  pageTurning(index: number | string): Promise<PageChangedParam> {
    console.info('[桥接层] pageTurning', index);
    this.lastPageIndex = index;
    const cmd = CoursewareCommand.PageTurning;
    const postInfo = {
      type: cmd,
      page: index,
      speed: 0,
      start: this.isFirstPageTurn,
      showHidePage: true,
    };
    this.postMessage(postInfo);
    this.isFirstPageTurn = false;
    this.fnPageTurningDbc();
    return this.callPromisify.record(cmd, CourseBridge.PAGE_TURNING_TIMEOUT, {
      code: ItsError.PageTurning,
    });
  }

  setOriginLocation(url: string): void {
    this.postMessage({
      type: CoursewareCommand.SetCWOriginLocation,
      data: { url },
    });
  }
  setIsLocationCW(useLocal: boolean): void {
    this.postMessage({
      type: CoursewareCommand.SetIsLocalCW,
      data: { useLocal },
    });
  }
  // 设置课件背景色
  setUpBodyBackground(color: string): void {
    this.postMessage({
      type: CoursewareCommand.SetUpBodyBackground,
      data: { color },
    });
  }

  receiveTransferMessage(data: unknown): void {
    this.postMessage({ type: CoursewareCommand.TransferMessageReceive, data });
  }
  sendMessageToCourseware(data: ParamsToCW): void {
    this.postMessage(data);
  }
  recoverCWState(data: unknown): void {
    this.postMessage({ type: CoursewareCommand.RecoverCWState, data });
  }

  sourceAvailable(data: SourceAvailableParam): void {
    this.postMessage({ type: CoursewareCommand.SourceAvailable, data });
  }

  animationDown(): void {
    this.postMessage({
      type: CoursewareCommand.AnimationChange,
      requestMsg: 'autoPlayAnimate',
      dir: 'next',
    });
  }

  animationUp(): void {
    this.postMessage({
      type: CoursewareCommand.AnimationChange,
      requestMsg: 'autoPlayAnimate',
      dir: 'previous',
    });
  }
  setElementOperate(isDrag: boolean): void {
    console.info('[桥接层] setElementOperate', isDrag);
    this.postMessage({
      type: CoursewareCommand.SetElementOperate,
      data: {
        switch: isDrag,
      },
    });
  }

  noticeGetCWState(): void {
    this.postMessage({ type: CoursewareCommand.NoticeGetCWState });
  }

  postMessage(data: ParamsToCW, transferable?: Transferable[]): void {
    const contentWindow = this.iframe.contentWindow;
    if (contentWindow) {
      console.info(
        `%c[${formatTime('Y-M-D H:m:s:S')}]%c 发给课件:`,
        'color:#E1112C;',
        '',
        data
      );
      contentWindow.postMessage(data, '*', transferable);
    } else {
      console.warn('postMessage error, iframe contentWindow null');
    }
  }

  private addMessageListener() {
    window.addEventListener('message', CourseBridge.__messageHandlerBinded);
    if (
      isThinkAcademyPC &&
      this.role === 'student' &&
      document.getElementById('interactionController')
    ) {
      document
        .getElementById('interactionController')
        .addEventListener('click', CourseBridge.__clickHandlerBinded);
    }
  }

  private messageHandler(e: MessageEvent) {
    const type = e.data.type;
    if (e.data.groupId || e.data.type === 'gameMsg') return;
    console.log(
      `%c[${formatTime('Y-M-D H:m:s:S')}]%c 来自课件:`,
      `color: #049be3;`,
      e.data.type,
      e.data
    );
    if (e.data.groupId || e.data.type === 'gameMsg') {
      console.info('[桥接层] 防止课件监听独立游戏消息');
      return;
    }

    switch (type) {
      case CoursewareEvent.Loaded:
        this.handleLoaded(e.data);
        break;
      case CoursewareEvent.Ready:
        this.handleReady();
        break;
      case CoursewareEvent.CheckSourceAvailable:
        this.handleCheckSourceAvailable(e.data.data);
        break;
      case CoursewareEvent.CoursewareLoadError:
        this.handleLoadError();
        break;
      case CoursewareEvent.HyperlinksMessage:
        this.handleHyperLink(e.data);
        break;
      case CoursewareEvent.GetCatalogueInfo:
        this.handleGetCatalogueInfo(e.data.info);
        break;
      case CoursewareEvent.GetBoundingClientRectByItemId:
        this.handleGetBoundingClientRectByItemId(e.data.data);
        break;
      case CoursewareEvent.GetResourceInfoList:
        this.handleGetResourceInfoList(e.data.data);
        break;
      case CoursewareEvent.TriggerMediaAction:
        this.handleTriggerMediaAction(e.data.data);
        break;
      case CoursewareEvent.PageChanged:
        this.handlePageChanged({
          ...e.data.data,
          interactiveMode: e.data.interactiveMode,
        });
        break;
      case CoursewareEvent.SendTestResult:
        this.handleSendTestResult(e.data.data);
        break;
      case CoursewareEvent.CoursewareLoadingProgress:
        this.handleCoursewareLoadingProgress(e.data.data);
        break;
      case CoursewareEvent.TransferMessageSend:
        this.handleTransferMessageSend(e.data.data);
        break;
      case CoursewareEvent.StoreCWState:
        this.handleStoreCWState(e.data.data);
        break;
      case CoursewareEvent.SendNeedChangePage:
        this.handleSendNeedChangePage(e.data.dir);
        break;
      case CoursewareEvent.GetCWState:
        this.handleGetCWState();
        break;
      case CoursewareEvent.ReportLog:
        this.handleReportLog(e.data.data);
        break;
      case CoursewareEvent.VideoStateChanged:
        this.handleVideoChanged(e.data.data);
        break;
      case CoursewareEvent.AudioStateChanged:
        this.handleAudioChanged(e.data.data);
        break;
    }
  }
  private handleVideoChanged(data: VideoStateChangedParam) {
    data.type = 'video';
    this.emit(CoursewareEvent.MediaStateChanged, data);
  }
  private handleAudioChanged(data: AudioStateChangedParam) {
    data.type = 'audio';
    this.emit(CoursewareEvent.MediaStateChanged, data);
  }
  private handleReportLog(data: MediaLoadReportParam) {
    handleDataPoints.handleCWReport(data);
  }
  private handleHyperLink(data) {
    this.emit(CoursewareEvent.HyperlinksMessage, data);
  }
  private handleCheckSourceAvailable(data) {
    this.emit(CoursewareEvent.CheckSourceAvailable, data);
  }
  private handleReady() {
    this.emit(CoursewareEvent.Ready);
  }
  private rejectPageTurning() {
    this.callPromisify.reject(CoursewareCommand.PageTurning, {
      code: 'E_PAGETURNING_TIMEOUT',
    });
  }

  private handleLoaded(data: WindowOnLoadedParam) {
    this.callPromisify.resolve(CoursewareEvent.Loaded, data);
  }

  private handleLoadError() {
    this.callPromisify.reject(CoursewareEvent.Loaded, {
      code: ItsError.LoadError,
    });
  }

  private handleCoursewareLoadingProgress(data) {
    console.info('[桥接层] handleCoursewareLoadingProgress', data);
    this.emit(CoursewareEvent.CoursewareLoadingProgress, data);
  }

  private handlePageChanged(data: PageChangedParam) {
    // console.info(
    //   '[桥接层] handlePageChanged',
    //   data,
    //   this.lastPageIndex,
    //   data.pageIndex
    // );
    if (this.lastPageIndex === 'virtual') {
      return;
    }
    if (this.lastPageIndex == null || data.pageIndex === this.lastPageIndex) {
      this.callPromisify.resolve(CoursewareCommand.PageTurning, data);
      if (this.role === 'student') {
        if (data.type === 'pageChanged' && data.includeInteractiveTemplate) {
          document
            .getElementById('gameDemoTip')
            .setAttribute('style', 'display:block');
          this.emit(CoursewareEvent.PageHasGame);
        } else {
          document
            .getElementById('gameDemoTip')
            .setAttribute('style', 'display:none');
        }
      }
      this.isGame = data.includeInteractiveTemplate;
    } else {
      this.pageTurning(this.lastPageIndex).catch((err) => {
        console.warn('page turning failed', err);
      });
    }
    this.emit(CoursewareEvent.PageChangeToTeacher, data);
  }
  private handleSendTestResult(data: QuestionResult[]) {
    this.emit(CoursewareEvent.SendTestResult, data);
  }
  private handleTriggerMediaAction(data: TriggerMediaActionParam) {
    this.emit(CoursewareEvent.TriggerMediaAction, data);
  }

  private handleGetCatalogueInfo(data) {
    this.callPromisify.resolve(CoursewareCommand.GetCatalogueInfo, data);
  }

  private handleGetResourceInfoList(data: GetResourceInfoListParam) {
    const callId = CoursewareCommand.GetResourceInfoList;
    this.callPromisify.resolve(callId, data);
  }

  private handleGetBoundingClientRectByItemId(
    data: GetBoundingClientRectByItemIdParam
  ) {
    const callId = this.getCallId(
      CoursewareCommand.GetBoundingClientRectByItemId,
      data.elementId
    );
    this.callPromisify.resolve(callId, data);
  }

  private handleTransferMessageSend(data: { type?: string }): void {
    // console.info('[桥接层] handleTransferMessageSend', data);
    this.emit(CoursewareEvent.TransferMessageSend, data);
  }

  private handleStoreCWState(data: unknown): void {
    this.emit(CoursewareEvent.StoreCWState, data);
  }

  private handleSendNeedChangePage(dir: string): void {
    this.emit(CoursewareEvent.SendNeedChangePage, dir);
  }

  private handleGetCWState(): void {
    this.emit(CoursewareEvent.GetCWState);
  }

  private getCallId(command: string, id: string): string {
    return `${command}-${id}`;
  }

  /**
   * 游戏演示toast
   */
  private showGameDemoToast(): void {
    if (this.isGame && isThinkAcademyPC && this.role === 'student') {
      let gameDemoToastTimer = null;
      document
        .getElementById('gameDemoToast')
        .setAttribute('style', 'display:block');
      gameDemoToastTimer = setTimeout(() => {
        document
          .getElementById('gameDemoToast')
          .setAttribute('style', 'display:none');
        clearTimeout(gameDemoToastTimer);
        gameDemoToastTimer = null;
      }, 2000);
    }
  }

  /**
   * 销毁实例，清理所有事件监听器和资源
   */
  destroy(): void {
    // 清理所有事件监听器
    this.removeAllListeners();

    // 移除全局消息监听器
    if (CourseBridge.__messageHandlerBinded) {
      window.removeEventListener(
        'message',
        CourseBridge.__messageHandlerBinded
      );
    }

    // 移除点击监听器
    if (
      CourseBridge.__clickHandlerBinded &&
      document.getElementById('interactionController')
    ) {
      document
        .getElementById('interactionController')
        .removeEventListener('click', CourseBridge.__clickHandlerBinded);
    }

    // 取消所有待处理的 Promise
    this.callPromisify.rejectAll({ code: CourseBridge.ELoadCanceled });

    // 清理防抖函数
    if (this.fnPageTurningDbc) {
      (this.fnPageTurningDbc as any).cancel?.();
    }
  }
}
