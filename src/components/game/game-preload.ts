import {
  GameCatalog,
  PreloadIframe,
  GamePreloadCmd,
  InitParams,
  ParamFromGame,
  GameCommand,
  GameEvent,
  ParamsToGame,
} from './game-type';
import {
  CatalogueItem,
  GetCatalogueInfoParam,
} from '../course-bridge/course-type';
import { InitParam, ItsError, ItsEvent, Role } from '../../../src/interface';
import { EventEmitter } from 'events';
import {
  showGameEndTip,
  showGameDemoTip,
  showGameStartTip,
} from '../../../src/libs/gameTip';
import getIframeStyle from './getIframeStyle';
export const IframeContainer = 'IframeContainer';
export const GameIframe = 'game_iframe_page_index'; // iframe父级自定义属性
export const GameId = 'game_id'; // gameid
import CallPromisify from '../course-bridge/call-promisify';

import cloneDeep from 'lodash/cloneDeep';
import { version } from '../../../package.json';
const GET_GAME_RECVRESTART_TIMEOUT = 2000; // 获取交互游戏是否支持重新玩

export enum ItsPageType {
  Courseware = 'course',
  Blank = 'blank',
}
class GamePreLoad extends EventEmitter {
  private preloadNum = 1; // 最多缓存个数
  public preloadIframes: PreloadIframe[]; // iframe缓存数组
  public catalogs: GameCatalog[]; // 游戏页数据
  public addtionalCatelogs = []; // 授课端目录数据

  public initParams: InitParams; // 课件初始化信息
  private static gamePreloadEvents: EventListener;
  public currentPageIndex: number; // 当前页码，用于接收消息后的判断
  private gameContainer = null;
  private gameIframe = 'game-iframe'; // 游戏iframe标识，active/no-active
  private ACTIVE = 'active';
  private NOACTIVE = 'no-active';
  private waitToRestartPlay = false; // 是否等待重新玩
  public urlPrefix: string; // 课件地址的前缀
  private ITS_WAIT_MAX_TIME = 3000; // 当前页时its时预加载等待的最长时间
  private callPromisify = new CallPromisify();
  syncReportTimer = null;
  gameplaying: boolean;
  preload: boolean;
  dataSplit: any;
  createLogs: any[];
  currentReset: boolean;
  cwStatus: any;
  planeTimer: void;
  spareUrlPrefix: any;
  gameLoadRole: any;
  preloadTimer: any;
  storeParams: { pageIndex: number; cb: () => void; isGame: boolean };
  sendRoomItsMessage: (data: any) => void;
  cacheParams: string;
  constructor() {
    super();
    this.catalogs = [];
    this.preloadIframes = [];
  }

  /**
   * 设置预加载需要的数据
   * @param preload 是否要预加载
   */
  public setPreloadInfo(params: InitParam): void {
    // console.info('[gamePreload] setPreloadInfo', params);
    this.initParams = {
      gamePreload: params.gamePreload,
      env: params.env,
      userId: params.userId || '123',
      role: params.role,
      client: params.client,
      width: params.screenWidth,
      height: params.screenHeight,

      // setDataPoint: params.setDataPoint,
      onEvent: params.onEvent,
      coursewareId: params.itsId,
      // checkResource: params.checkResource,
    };
    this.initParams.onEvent &&
      this.initParams.onEvent(ItsEvent.GameReady, {
        version: version,
        restartPlaying: true,
      });
    this.preload = params.gamePreload === '1';
    this.catalogs = [];
    this.dataSplit = undefined;
    this.preloadIframes = [];
    this.createLogs = [];
    this.currentReset = false;
    this.cwStatus = undefined;
    // 获取游戏备用地址的前缀
    this.spareUrlPrefix = params.remoteUrl[1]
      ? params.remoteUrl[1].split('index.html')[0]
      : '';
    this.gamePreloadAddMsg();
  }

  /**
   * 获取课件地址的前缀
   * @param url 课件地址
   */
  setCourseWareUrl(url: string): void {
    console.log('[gamePreload] setCourseWareUrl', url);
    this.urlPrefix = url.split('index.html')[0];
    // this.createHttp();
  }
  createHttp() {
    throw new Error('Method not implemented.');
  }

  private gamePreloadAddMsg() {
    window.removeEventListener('message', GamePreLoad.gamePreloadEvents);
    GamePreLoad.gamePreloadEvents = (e: MessageEvent) => {
      const type = e.data.type;
      if (e.data.groupId || e.data.type === 'score') {
        console.log(`%c 来自游戏:`, `color: #049be3;`, e.data);
        let nowGame = {} as PreloadIframe;
        if (e.data.groupId) {
          nowGame = this.getPreloadGameByGroupId(e.data.groupId);
          if (!nowGame) {
            console.info('[gamePreload] 未找到游戏', e.data.groupId);
            return;
          }
        } else {
          console.info('[gamePreload] 海外游戏score');
          nowGame = this.getCurrentPageGame();
        }

        // this.log({
        //   event: type,
        //   data: e.data,
        // });
        switch (type) {
          case GamePreloadCmd.RequestLoadStart:
            nowGame &&
              nowGame.supportKeepAndRestart &&
              this.log({
                data: e.data,
                msg: '新游戏开始加载游戏',
              });
            nowGame.loadStartTime = Date.now();
            break;
          case GamePreloadCmd.GamePreloadMsg:
            // 旧游戏开始-埋点---SDK load
            if (e.data.msg && e.data.msg.type === GamePreloadCmd.ResLoadStart) {
              const oldGame = this.getPreloadGameByPageIndex(
                this.currentPageIndex
              );
              oldGame &&
                this.log({
                  data: e.data,
                  msg: '老游戏开始加载游戏',
                });
            }
            break;
          case GamePreloadCmd.RequestEventReady:
            this.handleGameEventReady(e.data);
            this.dealGamePreloadStart(e.data);
            break;
          case GamePreloadCmd.RequestSyncInit:
            this.handleGameSyncInit(nowGame);
            break;
          case GamePreloadCmd.RequestrResLoadEnd:
            nowGame.loadEndTime = Date.now();
            if (nowGame.loadEndTime - nowGame.loadStartTime > 5000) {
              this.log(
                {
                  data: {
                    groupId: e.data.groupId,
                    loadDuration: nowGame.loadEndTime - nowGame.loadStartTime,
                  },
                  msg: '游戏加载超过5s',
                },
                'error'
              );
            }
            this.handleGameLoaded(nowGame, {
              status: 'success',
              errMsg: '',
              loadDuration: nowGame.loadEndTime - nowGame.loadStartTime,
            });
            break;
          case GamePreloadCmd.GameLoadState:
            break;
          case GamePreloadCmd.ResLoadProcess:
            break;
          case GamePreloadCmd.GameStart:
            if (nowGame.loadStartTime !== 0) {
              this.log({
                data: e.data,
                msg: '游戏加载完成',
              });
            } else {
              nowGame.loadStartTime = Date.now();
              this.log({
                data: e.data,
                msg: '游戏开始加载',
              });
            }

            break;
          case GamePreloadCmd.GameLoadError:
            nowGame.loadEndTime = Date.now();
            this.handleGameLoaded(nowGame, {
              status: 'error',
              errMsg: 'request_webglcontextlost',
              loadDuration: nowGame.loadEndTime - nowGame.loadStartTime,
            });
            this.handleGameErrorListener(nowGame);
            break;
          case GamePreloadCmd.RequestStaticResUrl:
            this.handleRequestStaticResUrl(nowGame);
            break;
          case GamePreloadCmd.RequestJsonData:
            this.handleRequestJsonData(nowGame, e.data);
            break;
          // 交互游戏同步消息（每次操作）
          case GameEvent.SendSyncData:
            this.handleGameSyncData(e.data);
            break;
          // 交互游戏支持重新玩的回调消息
          case GameEvent.RequestRrestartOver:
            this.handleGameRequestRrestartOver();
            break;
          case GameEvent.GameOver:
            if (this.gameplaying) {
              this.handleGameGameOver(e.data);
            }
            break;
          case GameEvent.GameScore: // 得到积分
            if (this.gameplaying) {
              this.handleGameScore(e.data);
            }
            break;
        }
      }
    };
    window.addEventListener('message', GamePreLoad.gamePreloadEvents);
  }
  handleGameSyncInit(nowGame: PreloadIframe) {
    console.info(
      '[gamePreload] 游戏初始化完成,这个时候才能接收信令,游戏从这个节点开始才能恢复重新玩',
      '游戏id:',
      nowGame.iframeID,
      '游戏index:',
      nowGame.pageIndex
    );
    nowGame.hasRequireSyncInit = true;
    if (this.currentPageIndex === nowGame.pageIndex) {
      if (this.waitToRestartPlay) {
        console.info('[gamePreload] 有历史恢复信令缓存,开始恢复重新玩');
        this.sendGameMsg(nowGame.iframeID, {
          type: GameCommand.RecvIsMaster,
          data: true,
        });
        this.gameplaying = true;
        this.restartPlaying();
        this.waitToRestartPlay = false;
      }
    }
  }
  handleRequestJsonData(nowGame, data: any) {
    // 读取json数据
    const resourceUrl = this.urlPrefix + 'resource/iframes';

    const gameInfo = this.getPreloadGameByGroupId(data.groupId);
    const filePath = `${resourceUrl}/${gameInfo.coursewareID}.json`;
    console.info('[gamePreload] request_json_data', filePath);
    // 验证文件是否存在

    fetch(filePath)
      .then((res) => {
        if (res.status !== 200) {
          throw new Error(`file ${filePath} not found`);
        }
        return res.json();
      })
      .then((jsonData) => {
        this.sendGameMsg(nowGame.iframeID, {
          type: 'recv_json_data',
          jsonData: jsonData,
          groupId: data.groupId,
        });
      })
      .catch((error) => {
        console.error('request_json_data Error:', error);
      });
  }
  handleRequestStaticResUrl(nowGame: PreloadIframe) {
    console.info('[gamePreload] 返回游戏外部资源', nowGame.iframeID);
    this.sendGameMsg(nowGame.iframeID, {
      type: 'recv_static_res_url',
      data: this.urlPrefix + 'resource/iframes',
    });
  }
  /**
   * 获取本地缓存的游戏-groupid
   * @param groupId
   * @returns
   */
  getPreloadGameByGroupId(groupId: string) {
    return this.preloadIframes.filter((ifr) => ifr.iframeID === groupId)[0];
  }

  /**
   * 生成iframe
   * @param data
   * @param show 是否要直接显示
   */
  public async createIframe(data: GameCatalog, show = false, cb?: () => void) {
    this.log(`[gamePreload] 开始创建游戏 ${data.pageIndex}`);
    if (this.createLogs.includes(data.pageIndex)) {
      console.warn(`第${data.pageIndex}页重复加载`, data, this.preloadIframes);
      return;
    }
    this.createLogs.push(data.pageIndex);
    if (show) {
      // this.preloadType = StoreType.SdkGame;
      this.storeParams = {
        pageIndex: data.pageIndex,
        cb,
        isGame: true,
      };
    }
    const iframeData: PreloadIframe = {
      iframeID: '', // iframeID
      pageIndex: data.pageIndex, // 页码
      gameID: data.gameInfo?.gameId, // 游戏ID
      gameName: data.gameInfo?.gameName, // 游戏ID
      supportKeepAndRestart: data.gameInfo?.supportKeepAndRestart, // 游戏ID
      isNewGame: data.gameInfo?.isNewPlatform, // 是否新游戏
      coursewareID: data.gameInfo?.coursewareId, // 游戏ID
      hasLoadStart: false, // 游戏加载开始
      loadStartTime: 0, // 游戏加载开始时间
      loadEndTime: 0, // 游戏加载结束时间
      hasRequireSyncInit: false, // 是否已经请求同步
      ifContainer: null,
      ifEl: null,
      isPreload: data.gameInfo?.isPrereload, // 是否支持预加载
      loadType: '',
      urlParams: '',
      loadUuid: '',
      id: data.gameInfo.id,
    };

    console.log('[gamePreload] createIframe', data.gameInfo?.localUrl);
    let gameUrl = data.gameInfo?.localUrl;
    // iframeData.loadType = GameLoadInfo[iframeData.loadTimes].loadType;

    // 获取地址需要拼接的参数

    this.createUrlParams(data.gameInfo?.localUrl, iframeData);
    gameUrl =
      gameUrl.indexOf('?') === -1
        ? `${gameUrl}?${iframeData.urlParams}`
        : `${gameUrl.split('?')[0]}?${iframeData.urlParams}`;

    const ifContainer = document.createElement('div');
    ifContainer.setAttribute(`${GameIframe}`, '' + data.pageIndex);
    // ifContainer.setAttribute('style', getIframeStyle.getIframeTransform());
    // ifContainer.style.top = show ? '0px' : '500%';
    ifContainer.style.width = '100%';
    ifContainer.style.height = '100%';
    ifContainer.style.position = 'absolute';
    ifContainer.style.top = '0';
    ifContainer.style.left = '0';
    ifContainer.style.opacity = show ? '1' : '0';
    ifContainer.style.zIndex = show ? '0' : '-1';
    if (show) {
      this.showGameContainer();
    }
    ifContainer.style.background = 'transparent';
    ifContainer.style.overflow = 'hidden';
    const ifEl = document.createElement('iframe');
    ifEl.style.width = '100%';
    ifEl.style.height = '100%';
    ifEl.style.bottom = 'auto';
    ifEl.style.right = 'auto';
    ifEl.style.pointerEvents =
      this.initParams.role === 'student' ? 'none' : 'initial';
    ifEl.style.border = 'none';
    ifEl.setAttribute(`${GameId}`, data.gameInfo?.gameId);
    ifEl.setAttribute('frameBorder', '0');
    ifEl.setAttribute('allow', 'autoplay');
    ifEl.setAttribute(this.gameIframe, show ? this.ACTIVE : this.NOACTIVE);
    iframeData.ifEl = ifEl;
    let groupId = null;
    // 获取游戏地址中的groupId
    const params = gameUrl.split('?')[1].split('&');
    for (const item of params) {
      const tmp = item.split('=');
      if (tmp[0] === 'groupId') {
        groupId = tmp[1];
        break;
      }
    }
    if (!groupId) this.log('游戏groupId不存在');
    ifContainer.setAttribute(`${GameId}`, groupId);
    if (this.initParams.client === 'pcm' && !data.gameInfo.isNewPlatform) {
      gameUrl = gameUrl.split('?')[0];
    }

    this.log({
      msg: '游戏加载地址',
      gameUrl,
    });

    ifEl.src = `${gameUrl}`;
    iframeData.iframeID = groupId; // iframeID
    iframeData.pageIndex = data.pageIndex; // 页码
    iframeData.gameID = data.gameInfo.gameId; // 游戏ID
    iframeData.ifContainer = ifContainer;
    iframeData.hasLoadStart = false; // 游戏加载开始
    this.preloadIframes.push(iframeData);
    console.info('[gamePreload] 添加到preloadIframes', iframeData.iframeID);
    ifContainer.appendChild(ifEl);
    this.gameContainer.appendChild(ifContainer);
  }

  /**
   * 根据本地地址和初始化参数构造游戏链接参数
   * @param localUrl 本地地址
   */
  createUrlParams(localUrl: string, iframeData: PreloadIframe): void {
    console.info('[gamePreload] createUrlParams', localUrl, iframeData);
    const urlParams = {};
    urlParams['groupId'] = `${iframeData.id}_${iframeData.gameID}`;

    if (localUrl) {
      const paramsStr = localUrl.split('?')[1];
      if (paramsStr) {
        const paramsArr = paramsStr.split('&');
        paramsArr.forEach((item) => {
          const [key, value] = item.split('=');
          if (key === 'channel') {
            urlParams[key] = 'hw';
          } else if (key === 'app') {
            urlParams[key] = 'hw';
          } else {
            urlParams[key] = value;
          }
        });
      }
    }
    const igNoreKeyList = [
      'setDataPoint',
      'onEvent',
      'checkResource',
      'width',
      'height',
      'gamePreload',
    ];
    for (const key in this.initParams) {
      if (igNoreKeyList.includes(key)) continue;
      urlParams[key] = this.initParams[key];
    }
    // 面授课需要传role=student,确保有声音
    if (this.initParams.client === 'pcm') {
      urlParams['role'] = 'student';
    }
    urlParams['isPreload'] = iframeData.isPreload;
    urlParams['supportKeepAndRestart'] = iframeData.supportKeepAndRestart;
    urlParams['isSync'] = this.initParams.client === 'pcm' ? 0 : 1;
    urlParams['coursewareId'] = iframeData.coursewareID;
    let paramsStr = '';
    for (const key in urlParams) {
      paramsStr += `${key}=${urlParams[key]}&`;
    }
    iframeData.urlParams = paramsStr.substr(0, paramsStr.length - 1);
  }

  /**
   * 获取当前页游戏的缓存数据
   * @returns 当前页游戏的缓存数据
   */
  getCurrentPageGame(): PreloadIframe {
    return this.getPreloadGameByPageIndex(this.currentPageIndex);
  }
  /**
   * 获取本地缓存的游戏-pageindex
   * @param groupId
   * @returns
   */
  getPreloadGameByPageIndex(pageIndex: number) {
    return this.preloadIframes.filter((ifr) => ifr.pageIndex === pageIndex)[0];
  }
  /**
   * 隐藏容器
   */
  hideGameContainer() {
    console.info('[gamePreload] hideGameContainer');
    this.gameContainer.style.zIndex = -1;
    this.gameContainer.style.opacity = 0;
  }

  /**
   * 显示容器
   */
  showGameContainer() {
    console.info('[gamePreload] showGameContainer');
    this.gameContainer.style.zIndex = 0;
    this.gameContainer.style.opacity = 1;
  }

  createIframeContainer(root: HTMLElement): void {
    console.info('[gamePreload] createIframeContainer');
    if (document.querySelector(`#${IframeContainer}`))
      document.querySelector(`#${IframeContainer}`).remove();
    this.gameContainer = document.createElement('div');
    this.gameContainer.setAttribute('id', IframeContainer);
    this.gameContainer.style.width = '100%';
    this.gameContainer.style.height = '100%';
    this.gameContainer.style.position = 'absolute';
    this.gameContainer.style.backgroundColor = '#000000';
    this.gameContainer.style.top = '0';
    this.gameContainer.style.zIndex = -1;
    this.gameContainer.style.opacity = '0';
    this.gameContainer.innerHTML = '';
    root.appendChild(this.gameContainer);
  }

  /**
   * 显示iframe
   * @param pageIndex pageIndex
   */
  public showIframe(pageIndex: number, cb?: () => void): void {
    console.log('[gamePreload] 展示加载游戏', pageIndex);
    this.showGameContainer();
    const hasGame = this.canShow(pageIndex);
    if (!hasGame) {
      // 当前游戏没有缓存，需要走先创建显示，再缓存的路径
      const gameInfo = this.pageIndexToGame(pageIndex);
      this.createIframe(gameInfo, true, cb);
      this.showGameContainer();
      // this.waitToStoregaGame(pageIndex, cb, true);
    } else {
      // this.storeGame(pageIndex);
    }
    this.reportGameAction({
      type: 'pageChanged',
      data: {
        enforce: undefined,
        ext: undefined,
        isVirtualPageOfPre: undefined,
        loaded: true,
        page: this.currentPageIndex,
        requestPageType: undefined,
        showHidePage: true,
        speed: 0,
        start: false,
      },
    });
    // 如果是老游戏，需要发送消息
    const gameInfo = this.pageIndexToGame(pageIndex);
    if (
      this.initParams.role === Role.Teacher &&
      gameInfo &&
      !gameInfo.gameInfo.isNewPlatform
    ) {
      console.info('[gamePreload] 主讲端老游戏，需要定时发送3s一次同步消息');
      this.syncReportTimer = setInterval(() => {
        console.log('[gamePreload] 主讲端老游戏，需要定时发送3s一次同步消息');
        this.reportGameAction({
          type: 'sendTransferReport',
          data: {},
        });
      }, 3000);
    }
  }

  /**
   * 判断当前页是否有游戏
   * @param pageIndex pageIndex
   * @returns
   *
   */
  public canShow(pageIndex: number): boolean {
    console.info('[gamePreload] canShow', pageIndex);
    const allGame = this.preloadIframes;
    let hasGame = false;
    console.log('[gamePreload] canShow', allGame);
    for (const e of allGame) {
      console.info(
        '[gamePreload] canShow',
        e,
        e.ifContainer,
        e.ifContainer.getAttribute(`${GameIframe}`)
      );
      if (e.ifContainer.getAttribute(`${GameIframe}`) == pageIndex) {
        hasGame = true;
        setTimeout(() => {
          // e.ifContainer.style.top = 0;
          e.ifContainer.style.opacity = 1;
          e.ifContainer.style.zIndex = 0;
          e.ifEl.setAttribute(this.gameIframe, this.ACTIVE);
          console.info(
            '[gamePreload] canShow hasLoadStart',
            e.iframeID,
            e.hasLoadStart
          );
          e.hasLoadStart && this.sendGamePannel(e.iframeID);
        }, 0);
      } else {
        // e.ifContainer.style.top = '500%';
        e.ifContainer.style.opacity = 0;
        e.ifContainer.style.zindex = -1;
        e.ifEl.setAttribute(this.gameIframe, this.NOACTIVE);
      }
    }
    return hasGame;
  }

  /**
   * 根据当前页码判断当前页类型进而缓存游戏
   * @param pageIndex 页码
   */
  waitToStoregaGame(
    pageIndex: number,
    cb?: () => void,
    isGame?: boolean
  ): void {
    console.info('[gamePreload] 是否要预加载下一页', pageIndex);
    if (!this.preload || this.catalogs.length === 0) return;
    const page: GameCatalog = this.catalogs.filter(
      (cwPage) => cwPage.pageIndex === pageIndex
    )[0];
    const nextPage: GameCatalog = this.catalogs.filter(
      (cwPage) => cwPage.pageIndex === pageIndex + 1
    )[0];
    if (
      !page ||
      !nextPage ||
      !nextPage.isGame ||
      !nextPage.gameInfo.isPrereload
    ) {
      console.warn('[gamePreload] 不满足预加载条件');
      return;
    }
    this.storeParams = {
      pageIndex,
      cb,
      isGame,
    };
    // 普通课件，和小程序处理一样
    this.preloadTimer = setTimeout(() => {
      console.info('[gamePreload] 3秒后，开始预加载', pageIndex);
      this.storeGame(pageIndex, cb, isGame);
    }, this.ITS_WAIT_MAX_TIME);
  }

  /**
   * 根据当前页码缓存游戏
   * @param pageIndex 页码
   */
  public storeGame(pageIndex: number, cb?: () => void, isGame?: boolean): void {
    console.info('[gamePreload] storeGame', pageIndex, this.preload);
    if (!this.preload) return;
    const num = this.preloadNum; // 存储要缓存的游戏数量
    for (let i = 1; i <= num; i += 1) {
      const nowIdx = pageIndex + i;
      if (this.pageIsGame(nowIdx)) {
        // 下一页是游戏
        const gameInfo = this.pageIndexToGame(nowIdx);
        const exist = this.preloadIframes.filter((g) => g.pageIndex == nowIdx);
        // 下一页是游戏&没有缓存&有预加载能力
        if (exist.length === 0 && gameInfo.gameInfo.isPrereload) {
          console.info(
            '[gamePreload] 下一页是游戏&没有缓存&有预加载能力,所以开始预加载',
            nowIdx
          );
          this.createIframe(gameInfo, false, cb);
        }
      }
    }
  }

  /**
   * 发送游戏开始消息
   * @param groupId 游戏唯一标识
   */
  private sendGamePannel(groupId: string) {
    console.info('[gamePreload] sendGamePannel', groupId);
    this.sendGameMsg(groupId, {
      type: GamePreloadCmd.ShowGamePannel,
    });
  }

  private sendGameMsg(groupId: string, data: any) {
    console.info('[gamePreload] 传给学生端', groupId, data);
    const gameIframe = this.getPreloadGameByGroupId(groupId);
    if (gameIframe === undefined) {
      console.error(
        '[gamePreload] sendGameMsg 未找到游戏,给游戏发送消息失败',
        groupId
      );
      return;
    }
    const contentWindow = gameIframe.ifEl.contentWindow;
    if (contentWindow) {
      contentWindow.postMessage(data, '*');
    } else {
      console.error(
        '[gamePreload] 当前iframe 还没有创建完成,暂时无法通信',
        groupId
      );
    }
  }
  /**
   * 根据页码获取游戏i信息
   * @param pageIndex 页码
   */
  public pageIndexToGame(pageIndex: number): GameCatalog {
    return this.catalogs.filter((g) => g.pageIndex == pageIndex)[0];
  }

  /**
   * 判断当前页是否是游戏----- SDK加载还是内容云加载的
   * @param data
   * @returns boolean
   */
  public pageIsGame(pageIndex: number): boolean {
    console.info('[gamePreload] 判断是否是游戏页', pageIndex);
    if (this.catalogs.length === 0) return;
    for (const page of this.catalogs) {
      if (page.pageIndex === pageIndex && page.isGame) {
        console.info(`[gamePreload] 该页${pageIndex} 是游戏`);
        return true;
      }
    }
    return false;
  }

  /**
   * 专注模式切换
   * @param width
   * @param height
   */
  resizeCw(width: number, height: number) {
    // 重置初始宽高
    getIframeStyle.setRoot(width, height);
    // 获取样式
    getIframeStyle.getIframeTransform();
    const gameEls = this.gameContainer.children;
    // 更改游戏缩放
    Array.from(gameEls).forEach((el: HTMLElement) => {
      if (el.getAttribute(`${GameIframe}`)) {
        el.style.transform = getIframeStyle.getTransformStr();
      }
    });
  }

  /**
   * 关闭所有iframe
   */
  public closeAllIframe(pageIndex: number): void {
    this.syncReportTimer && clearInterval(this.syncReportTimer);

    this.currentReset = pageIndex === this.currentPageIndex; // 当前页刷新操作
    this.currentPageIndex = pageIndex;
    this.preloadTimer && window.clearTimeout(this.preloadTimer);
    const page = this.catalogs.filter(
      (cwPage) => cwPage.pageIndex === pageIndex
    )[0];
    if (page.isGame) {
      // 要翻到的页也是游戏
      console.info(
        '[gamePreload] 要翻到的页也是游戏,不需要hideGameContainer',
        pageIndex
      );
    } else {
      this.hideGameContainer();
    }
    if (!this.preload) {
      this.preloadIframes.forEach(
        (ifr) => ifr.ifContainer && ifr.ifContainer.remove()
      );
      this.preloadIframes = [];
      this.createLogs = [];
    }
    const allGame = cloneDeep(this.preloadIframes);
    for (let i = 0; i < allGame.length; i += 1) {
      const game = allGame[i];
      if (!this.delIframeData(game, pageIndex, i)) {
        game.ifContainer.style.opacity = 0;
        game.ifContainer.style.pointerEvents = 'none';
        game.ifEl.setAttribute(this.gameIframe, this.NOACTIVE); // 标识不活跃
        // 翻页时使所有游戏静音
        game.pageIndex != this.currentPageIndex &&
          this.sendGameReset(game, GamePreloadCmd.RecvHideGamePanel);
        if (game.pageIndex === this.currentPageIndex && this.currentReset) {
          try {
            game.ifEl.src = 'about:blank';
          } catch (e) {
            console.warn('清除iframe-closeall');
          }
          game.ifContainer && game.ifContainer.remove();
          this.preloadIframes.splice(i, 1);
          this.createLogs = this.createLogs.filter(
            (logIdx) => logIdx !== game.pageIndex
          );
        }
      }
    }
  }
  public closeAllGame(): void {
    this.log('清楚所有预加载游戏');
    this.preloadIframes = this.preloadIframes.filter((ifr) => {
      if (ifr.pageIndex === this.currentPageIndex) {
        return true;
      } else {
        ifr.ifContainer && ifr.ifContainer.remove();
        return false;
      }
    });
    this.createLogs = this.createLogs.filter(
      (logIdx) => logIdx === this.currentPageIndex
    );
  }

  public delIframeData(
    d: PreloadIframe,
    pageIndex: number,
    idx: number
  ): boolean {
    const maxGameIdx = pageIndex + this.preloadNum;
    if (
      d.pageIndex < pageIndex ||
      d.pageIndex > maxGameIdx ||
      (d.pageIndex !== this.currentPageIndex && !d.supportKeepAndRestart)
    ) {
      console.info(
        '[gamePreload] delIframeData 清除游戏',
        d.iframeID,
        d.pageIndex
      );
      try {
        d.ifEl.src = 'about:blank';
      } catch (e) {
        console.warn('清除iframe-del', e);
      }
      d.ifContainer && d.ifContainer.remove();
      this.preloadIframes.splice(idx, 1);
      this.createLogs = this.createLogs.filter(
        (logIdx) => logIdx !== d.pageIndex
      );
      return true;
    }
    return false;
  }

  /**
   * 根据消息处理游戏开始
   * @param data 游戏消息数据
   */
  public dealGamePreloadStart(data: { groupId: string }): void {
    console.info('[gamePreload] 到这步的时候可以展示游戏界面', data);
    // 监听到消息后，看对应的游戏是否正在显示
    this.delIframe(this.currentPageIndex);

    const { groupId } = data;
    const gamePreloadInfo = this.preloadIframes.filter(
      (t) => t.iframeID === groupId
    )[0];

    if (gamePreloadInfo === undefined) return;
    // 根据消息中的groupId，判断游戏是否是当前显示的，是就发消息
    gamePreloadInfo.hasLoadStart = true;
    if (this.currentPageIndex === gamePreloadInfo.pageIndex) {
      this.sendGamePannel(groupId);
    }
  }
  /**
   * 删除当前页之前的iframe，以及从当前页开始往后超出缓存数的iframe
   * @param pageIndex
   */
  public delIframe(pageIndex: number): void {
    const iframes = this.preloadIframes;
    iframes.forEach((d, idx) => {
      this.delIframeData(d, pageIndex, idx);
    });
  }

  /**
   * 向游戏发送游戏重置消息
   * @param data 游戏数组数据
   * @param cmd 游戏重置消息
   */
  private sendGameReset(data: PreloadIframe, cmd: GamePreloadCmd): void {
    console.info('[gamePreload] 向游戏发送游戏重置消息', data.iframeID, cmd);
    this.sendGameMsg(data.iframeID, {
      type: cmd,
    });
  }

  /**
   * 获取目录中的游戏信息
   * @param pageData 原始目录
   * @param dataSplit 是否拆分
   */
  getPageGameInfo(pageData: GetCatalogueInfoParam, dataSplit: boolean) {
    return this.noDataSplitCatelogs(pageData);
  }

  /**
   * 未数据拆分
   * @returns promise
   */
  noDataSplitCatelogs(pageData: GetCatalogueInfoParam): Promise<any> {
    return new Promise((resolve) => {
      pageData.forEach((gameInfo: CatalogueItem, index: number) => {
        const { item } = gameInfo;
        const gameItem = {
          pageId: item.id,
          type: ItsPageType.Courseware,
          title: item.title,
          isHide: item.isHide,
          hasIframe: item.hasIframe,
          note: item.note,
          index,
        }; // 元数据
        if (!gameItem.isHide && gameInfo.isGame) {
          this.pushCatelogs(
            gameInfo.item.items,
            gameItem,
            index,
            item.main,
            gameInfo
          );
        } else {
          this.catalogs.push({
            ...gameItem,
            pageIndex: index,
            hasApplets: false,
            isGame: false,
          });
        }
      });
      resolve([this.catalogs]);
    });
  }
  /**
   * 返回游戏具体数据
   * @param items
   */
  filterGameInfo(
    items: CatalogueItem['item']['items'],
    main: CatalogueItem['item']['main'],
    gameInfo: CatalogueItem
  ): any {
    // 首先判断是不是小程序
    let content = '';
    items.map((item) => {
      if (item.content) {
        content += item.content;
      }
    });
    const data = items[0];
    // 不是小程序就是交互游戏
    const hiddenContainer = document.createElement('div');
    hiddenContainer.style.display = 'none';
    hiddenContainer.style.position = 'fixed';
    hiddenContainer.style.zIndex = '-1000';
    hiddenContainer.innerHTML = content;
    let iframeSrc = '';
    Array.from(hiddenContainer.children).forEach((c) => {
      if (c.nodeName === 'IFRAME') {
        iframeSrc = c.getAttribute('srcf');
      }
    });
    return {
      hasApplets: false,
      isGame: gameInfo.isGame,
      gameInfo: {
        supportKeepAndRestart: !!gameInfo.supportKeepAndRestart,
        titleId: gameInfo.titleId,
        gameName: data.name, // 游戏名称
        gameId: gameInfo.gameId, // 游戏id， item.gameId
        coursewareId: gameInfo.coursewareId, //
        isPrereload: !!gameInfo.isPrereload, // 是否支持预加载
        isNewPlatform: !!gameInfo.gameIsNewPlatform,
        checkLocalUrl: iframeSrc ? iframeSrc.split('?')[0] : '', // 校验使用
        localUrl: this.urlPrefix + iframeSrc, // 游戏本地地址
        remoteUrl: data.attr.url, // 远程地址 attr.url
        spareUrl: this.spareUrlPrefix
          ? this.spareUrlPrefix + iframeSrc
          : iframeSrc, // 远程地址
        posX: data.posX, // item.posX
        posY: data.posY, // item.posY
        style: data.style,
        id: data?.id, // 内容云传输ID
      },
    };
  }
  pushCatelogs(
    items: CatalogueItem['item']['items'],
    gameItem,
    index,
    main: CatalogueItem['item']['main'],
    gameInfo: CatalogueItem
  ): void {
    const game = this.filterGameInfo(items, main, gameInfo);
    //游戏
    this.catalogs.push({
      ...gameItem,
      pageIndex: index,
      ...cloneDeep(game),
    });
  }

  /**
   * 交互游戏 同步数据
   * @param {ParamFromGame}
   * 接着玩、重新玩 只要收到消息立马打回自己，不广播
   * 正常同步模式、单独授权 只要收到消息立马打回自己，并且广播
   */
  protected handleGameSyncData(data: ParamFromGame): void {
    // 判断是否是当前页游戏
    const gameInfo = this.getPreloadGameByGroupId(data.groupId);
    if (!gameInfo) return;
    // 判断是否是当前页游戏
    if (gameInfo.pageIndex !== this.currentPageIndex) return;

    // if (this.initParams.role === Role.Student) return;

    // 操作端 直接发给自己 增加操作端连贯性
    // 新游戏不接受自己的心跳

    // 判断是否是老游戏
    const game = this.pageIndexToGame(this.currentPageIndex);

    if (game.gameInfo.isNewPlatform && !data?.data?.isHeartBreak) {
      this.sendGameMsg(data.groupId, {
        type: GameCommand.RecvSyncData,
        data: data.data,
      });
    }
    if (this.initParams.role === Role.Teacher) {
      // 广播
      this.reportGameAction({
        type: 'sendTransferReport',
        data: data.data,
        groupId: data.groupId,
        supportKeepAndRestart: gameInfo.supportKeepAndRestart,
      });
    }
  }

  /**
   * ->端 游戏sync数据 or 3sSync数据
   * @detail 广播
   * @param {any}
   */
  private reportGameAction(data, gameAction = '') {
    console.info('[gamePreload] 桥阶层游戏抛出给客户端', data.type, gameAction);
    this.sendRoomItsMessage && this.sendRoomItsMessage(data);
  }
  /**
   * 重新玩
   * @detail -> 交互游戏 2s内未收到回调，进行游戏重载
   * @param {any}
   */
  public async restartPlaying() {
    console.info('[gamePreload] restartPlaying');
    try {
      const res = await this.checkGameSupportRestart();
    } catch (e) {
      console.warn('[gamePreload] 不支持重新玩,重新刷新地址');
      // 游戏重载
      const gameIframe = <HTMLIFrameElement>(
        document.querySelector('iframe[game-iframe="active"]')
      );
      // const gameInfo = this.pageIndexToGame(this.currentPageIndex);
      const iframeData = this.getCurrentPageGame();
      console.info(
        '[gamePreload] restartPlaying',
        iframeData ? iframeData.iframeID : '没有获取到iframeData'
      );
      if (iframeData.isNewGame) {
        gameIframe.src = gameIframe.src + '&date=' + Date.now();
        console.info('[gamePreload] restartPlaying 新游戏', gameIframe.src);
      } else {
        this.cacheParams = gameIframe.src.split('?')[1];
        const url = gameIframe.src.split('?')[0];
        gameIframe.src =
          url + `?groupId=${iframeData.iframeID}&date=${Date.now()}`;
        console.info('[gamePreload] restartPlaying 旧游戏', gameIframe.src);
      }
    }
  }

  /**
   * CHECK 当前游戏是否支持重新玩
   * @detail
   * @param {any}
   */
  public checkGameSupportRestart() {
    console.info('[gamePreload] 检查游戏是否支持重新玩');
    const iframeData = this.getCurrentPageGame();
    if (!iframeData) {
      console.warn(
        '[gamePreload] checkGameSupportRestart 没有获取到iframeData'
      );
      return;
    }
    console.info('[gamePreload] 发送重新玩信息', iframeData.iframeID);
    this.sendGameMsg(iframeData.iframeID, {
      type: GameCommand.RecvRestart,
    });
    return this.callPromisify.record(
      GameCommand.RecvRestart,
      GET_GAME_RECVRESTART_TIMEOUT,
      { code: 'timeout and game unsupported restart' }
    );
  }

  /**
   * 交互游戏收到重新玩的回调消息（若支持）
   * @detail
   * @param {any}
   */
  protected handleGameRequestRrestartOver() {
    console.info('[gamePreload] 收到游戏支持重新玩的回调消息');
    this.callPromisify.resolve(GameCommand.RecvRestart, 'support restart');
  }

  /**
   * 发消息给交互游戏
   * @detail
   * @param {ParamsToGame}
   * @return void
   */
  private postMessageToGame(
    data: ParamsToGame,
    transferable?: Transferable[]
  ): void {
    const contentWindow = (<HTMLIFrameElement>(
      document.querySelector('iframe[game-iframe="active"]')
    ))?.contentWindow;
    if (contentWindow) {
      contentWindow.postMessage(data, '*', transferable);
      console.info('[gamePreload] 所有发给游戏的数据 success', data);
    } else {
      console.warn('postMessage error, gameIframe contentWindow null');
    }
  }

  receiveTransferMessage(cwAction) {
    // console.info('[gamePreload] 接收客户端传入桥接触游戏的信令', cwAction.type);
    if (cwAction.type === 'Restart') {
      console.info('[gamePreload] 收到课件内玩 Restart');
      showGameDemoTip(false);
      showGameStartTip();
      const iframeData = this.getCurrentPageGame();
      console.info(
        '[gamePreload] Restart ',
        iframeData ? '获取' + iframeData.iframeID : '没有获取到iframeData'
      );
      iframeData.ifEl.style.pointerEvents = 'initial';
      if (iframeData.isNewGame) {
        console.info('[gamePreload] Restart 新游戏');
        if (iframeData.hasRequireSyncInit) {
          console.info('[gamePreload] Restart 游戏初始化完成');
          this.sendGameMsg(iframeData.iframeID, {
            type: GameCommand.RecvIsMaster,
            data: true,
          });
          this.gameplaying = true;
          this.restartPlaying();
        } else {
          console.info('[gamePreload] Restart 游戏初始化未完成,先等待');
          this.waitToRestartPlay = true;
        }
      } else {
        console.info('[gamePreload] Restart 旧游戏', this.cacheParams);
        this.gameplaying = true;
        this.restartPlaying();
      }
    }
    if (cwAction.type === 'closeGame') {
      console.info('[gamePreload] 收到课件内玩 closeGame');
      this.waitToRestartPlay = false;
      showGameDemoTip(true);
      this.gameplaying && showGameEndTip();
      const iframeData = this.getCurrentPageGame();
      console.info(
        '[gamePreload] closeGame ',
        iframeData ? '获取' + iframeData.iframeID : '没有获取到iframeData'
      );
      iframeData.ifEl.style.pointerEvents = 'none';
      this.gameplaying = false;
      if (iframeData.isNewGame) {
        console.info('[gamePreload] closeGame 新游戏');
        this.sendGameMsg(iframeData.iframeID, {
          type: GameCommand.RecvIsMaster,
          data: false,
        });
      } else {
        console.info('[gamePreload] closeGame 旧游戏');
        if (this.cacheParams) {
          console.info('有缓存参数，重新加载');
          const gameIframe = <HTMLIFrameElement>(
            document.querySelector('iframe[game-iframe="active"]')
          );
          const url = gameIframe.src.split('?')[0];
          gameIframe.src = `${url}?${this.cacheParams}`;
          this.cacheParams = '';
        }
      }
    }
    if (cwAction.type === 'sendTransferReport') {
      if (this.gameplaying) return;
      if (!cwAction.groupId) return;
      this.sendGameMsg(cwAction.groupId, {
        type: GameCommand.RecvSyncData,
        data: cwAction.data,
      });
    }
    if (cwAction.type === 'closeAll') {
      this.closeAllGame();
    }
  }

  /**
   * 交互游戏 学生 gameover数据
   * @param {ParamFromGame}
   * @return void
   */
  private handleGameGameOver(data: ParamFromGame): void {
    if (this.initParams.role == Role.Teacher) return;
    // 判断是否是新游戏
    const gameInfo = this.pageIndexToGame(this.currentPageIndex);
    if (!gameInfo.gameInfo.isNewPlatform) return;
    const submitData = {
      rightRate: data.data.gameOver.percentage,
      answerPics: data.data.responsePic || [],
      judge: data.data.result.map((e) =>
        e.answer_res === 'answer_right' ? 1 : 0
      ),
    };
    console.info('[gamePreload] 新游戏得分', submitData);

    this.initParams.onEvent(ItsEvent.GameOver, submitData);
  }
  getRightRate(rightNum, totalSum) {
    return Math.round((rightNum / totalSum) * 100);
  }
  handleGameScore(data): void {
    const scoreData = data.scoreData;
    const rightNum = scoreData.detail.resultDetail.rightNum;
    const totalNum = scoreData.detail.resultDetail.questionSum;
    const rightRate = this.getRightRate(rightNum, totalNum);
    const answerPics = scoreData.detail.resultDetail.answerPics;
    const judge = scoreData.judge;
    const submitData = {
      rightRate: rightRate,
      answerPics,
      judge,
    };
    console.info('[gamePreload] 老游戏得分', judge, rightRate);
    this.initParams.onEvent(ItsEvent.GameOver, submitData);
  }
  /**
   * 监听到游戏内部崩溃后重载
   * @param game
   */
  handleGameErrorListener(game?: PreloadIframe) {
    game && this.closeAllIframe(this.currentPageIndex);
  }
  /**
   * 交互游戏 重载
   */
  handleGameLoaded(
    game?: PreloadIframe,
    data?: { status: string; errMsg: string; loadDuration: number }
  ) {
    console.info('[gamePreload] handleGameLoaded', data);
    this.initParams.onEvent(ItsEvent.Statistic, {
      eventName: 'hw_courseware_game',
      params: {
        result: data.status, //游戏加载状态，success、error
        url: game.ifEl.src, //游戏相对路径，公共参数
        isNewGame: game.isNewGame, //是否是新游戏，公共参数
        loadDuration: data.loadDuration, //游戏加载耗时，result=success时生效，单位ms
        msg: data.errMsg, //result=error时生效
      },
    });
  }
  /**
   * 交互游戏 具备接收消息能力
   * @detail 此时-> game 是否为主动发送心跳一端
   * @param {ParamFromGame}
   */
  protected handleGameEventReady(data: ParamFromGame): void {
    console.info('[gamePreload] 交互游戏 具备接收消息能力', data);
    const { groupId } = data;
    this.sendGameMsg(groupId, {
      type: GameCommand.RecvIsMaster,
      data: this.initParams.role == Role.Teacher ? true : false,
    });
  }

  log(data: any, level = 'info') {
    // 如果data是字符串,变成对象
    if (typeof data === 'string') {
      data = {
        msg: data,
      };
    }
    console[level](data.msg);
    this.initParams.onEvent &&
      this.initParams.onEvent(ItsEvent.Console, {
        ...data,
        level,
      });
  }

  destroy() {
    this.preloadTimer && window.clearTimeout(this.preloadTimer);
    this.syncReportTimer && clearInterval(this.syncReportTimer);
    window.removeEventListener('message', GamePreLoad.gamePreloadEvents);
    this.preloadIframes.forEach((game) => {
      game.ifContainer && game.ifContainer.remove();
    });
    this.preloadIframes = [];
    this.createLogs = [];
  }
}

const gamePreload = new GamePreLoad();

export default gamePreload;
