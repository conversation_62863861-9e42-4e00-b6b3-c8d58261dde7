/**
 * 游戏预加载数据格式
 */

import { ItsEvent } from "src/interface";

export const GameIframe = 'game_iframe_page_index'; // iframe父级自定义属性
export const GameId = 'game_id'; // gameid
export const IframeContainer = 'IframeContainer';

// iframe缓存数据
export interface PreloadIframe {
  iframeID: string; // iframeID
  pageIndex: number; // 页码
  gameID: string; // 游戏ID
  gameName: string;
  supportKeepAndRestart: boolean; // 是否支持重新玩
  isNewGame: boolean; // 是否是新游戏

  game_uuid?: string;
  page_change_uuid?: string; // 翻页ID
  game_preload?: boolean; // 游戏是否是预加载的
  coursewareID: string;
  isPreload?: boolean; // 游戏是否支持预加载
  ifContainer: any; // iframe父级div
  ifEl: any; // iframeDOM
  loadType?: string; // 加载类型
  urlParams?: string; //地址参数拼接
  loadUuid?: string; // 加载唯一标识
  id?: string; // 内容云传输ID
  hasLoadStart: boolean; // 是否已经开始加载
  loadStartTime: number; // 开始加载时间
  loadEndTime: number; // 加载结束时间
  hasRequireSyncInit: boolean; // 是否已经请求同步数据
}

export enum GamePreloadCmd {
  GamePreloadMsg = 'gameMsg', // 交互游戏预加载消息
  RequestLoadStart = 'request_res_load_start', // 游戏开始加载
  RequestEventReady = 'request_event_ready', // 游戏开始接收消息
  ShowGamePannel = 'recv_show_gamePanel', // 游戏就绪
  ResLoadProcess = 'res_load_process', // 游戏加载进度
  RequestrResLoadEnd = 'request_res_load_end', // 游戏加载完成
  RecvHideGamePanel = 'recv_hide_gamePanel', // 重新使游戏处于暂停状态
  GameLoadTry = 'game_load_try_', // 游戏加载尝试
  PageLoadComplete = 'page_load_complete', // 当前页加载完成
  GameLoadState = 'gameLoadState', // 内容云加载游戏-完成
  GameStart = 'game_start', // 内容云加载游戏-完成
  GameLoadError = 'request_webglcontextlost', // 游戏内部加载错误
  ResLoadStart = 'res_load_start', // 老游戏开始加载
  RequestStaticResUrl ='request_static_res_url', // 请求静态资源地址
  RequestJsonData = 'request_json_data', // 请求json数据
  SendSyncData = 'send_sync_data', // 游戏同步事件
  SendSync3sData = 'send_sync_3s_data', // 交互游戏同步3s心跳全量数据消息
  RequestResLoadStart = 'request_res_load_start', // 游戏开始加载
  RequestResLoadEnd = 'request_res_load_end', // 游戏加载完成
  RequestKeepPlaying = 'request_keep_playing', // 授课端收到recv_keep_playing接着玩的回调消息
  RequestRrestartOver = 'request_restart_over', // 游戏收到recv_restart且支持重新玩的回调
  RequestSyncInit = 'request_sync_init', // 游戏可以接心跳数据
  AnswerSyncSend = 'answer_sync_send', // 游戏作答数据
  GameOver = 'game_over', // 游戏完成时数据上报全量数据
  RequestLevelInfo = 'request_level_info', // 游戏关卡数据
}

export interface PageData {
  sdkGameLoad?: string;
  gameId?: string;
  pageId?: string;
  pageIndex: number;
  localUrl?: string;
  remoteUrl?: string;
  spareUrl?: string;
  hasApplets: string; // 是否是小程序，0/1
  isGame?: string; // 是否是游戏，0/1
}

export interface GameCatalog {
  pageId?: string;
  pageIndex?: number;
  hasApplets?: boolean; // 是否是小程序
  isGame?: boolean;
  requestFailed?: number; // 请求失败
  gameInfo?: {
    supportKeepAndRestart?: boolean; // 是否支持重新玩和接着玩,授课端需要
    sdkGameLoad?: boolean; // 是否由sdk渲染游戏
    isPrereload?: boolean; // 是否支持预加载
    isNewPlatform?: boolean; // 是否是新平台
    gameName?: string; // 游戏名称,授课端需要
    coursewareId?: string; //
    gameId?: string; // 游戏id
    localUrl?: string; // 游戏本地地址
    checkLocalUrl?: string;
    remoteUrl?: string; // 远程地址
    spareUrl?: string; // 备用地址
    posX?: string;
    posY?: string;
    style?: string;
    titleId?: string; // titleId
    id?: string; // 内容云传输ID
  };
}

export interface InitParams {
  gamePreload?: string;
  chapterId?: string;
  userId?: string;
  role?: string;
  client?: string;
  width?: number;
  height?: number;
  env?: string;
  clientDescription?: string;
  // setDataPoint: (data: PointData) => void; // 埋点
  onEvent: (event: ItsEvent, data: unknown) => void; // 埋点
  // checkResource: FnCheckResource; // 埋点
  coursewareId: string;
}


// game ->
export enum GameEvent {
  RequestEventReady = 'request_event_ready', // 游戏准备就绪 （可以接受recv_is_master）消息
  SendSyncData = 'send_sync_data', // 游戏同步事件
  SendSync3sData = 'send_sync_3s_data', // 交互游戏同步3s心跳全量数据消息
  RequestResLoadStart = 'request_res_load_start', // 游戏开始加载
  RequestResLoadEnd = 'request_res_load_end', // 游戏加载完成
  RequestKeepPlaying = 'request_keep_playing', // 授课端收到recv_keep_playing接着玩的回调消息
  RequestRrestartOver = 'request_restart_over', // 游戏收到recv_restart且支持重新玩的回调
  RequestSyncInit = 'request_sync_init', // 游戏可以接心跳数据
  AnswerSyncSend = 'answer_sync_send', // 游戏作答数据
  GameOver = 'game_over', // 游戏完成时数据上报全量数据
  GameScore = 'score', // 游戏得分
  RequestLevelInfo = 'request_level_info', // 游戏关卡数据
}

//  -> game
export enum GameCommand {
  RecvIsMaster = 'recv_is_master', // 是否为主动发送心跳的一端
  RecvSyncData = 'recv_sync_data', // 发送同步消息
  RecvSync3sData = 'recv_sync_3s_data', // 发送3s心跳消息
  RecvShowGamePanel = 'recv_show_gamePanel', // 可以开始游戏
  RecvKeepPlaying = 'recv_keep_playing', // ->授课端 开启全员授权（接着玩）
  RecvRestart = 'recv_restart', // 开启全员授权（重新玩）
  RecvCancelKeepPlaying = 'recv_cancel_keep_playing', //取消接着玩的事件(游戏收到此事件会在短时间内增加心跳频率)
}

// -> game
export type ParamsToGame = {
  type: GameCommand;
  data?: unknown;
};

// game ->
export type ParamFromGame = {
  type?: string;
  data?: any;
  groupId?: string;
};

// 课件中游戏事件
export enum GameLoadState {
  GameLoadStart = 'game_load_start', // 开始加载
  GameLoadComplete = 'game_load_complete', // 加载完成
}
