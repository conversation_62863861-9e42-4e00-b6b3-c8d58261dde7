let styleStr = '',
  transformStr = '';
let rootWidth, rootHeight;
let initWidth = 0,
  initHeight = 0,
  initRate = 0;
const setInitValue = (width: string, height: string): void => {
  initWidth = parseInt(width);
  initHeight = parseInt(height);
};
const setRoot = (width: number, height: number): void => {
  rootWidth = width;
  rootHeight = height;
  styleStr = '';
  transformStr = '';
};
/**
 * 获取游戏显示时需要的样式信息
 * @returns transform字符串
 */
const getIframeTransform = (): string => {
  // 根据当前实际的分辨率获取iframe定位用的数据
  // 显示时才设置top值
  if (styleStr) return styleStr;
  const normalWidth = initWidth,
    normalHeight = initHeight; // 标准宽高
  const realWidth = rootWidth; // 屏幕宽度
  const realHeight = rootHeight; // 屏幕高度
  let rate = 0; // scale的值
  let rateW = Infinity,
    rateH = Infinity;
  if (realWidth <= normalWidth || realWidth > normalWidth) {
    // 宽小于标准
    rateW = Number((realWidth / normalWidth).toFixed(6));
  }
  if (realHeight <= normalHeight || realHeight > normalHeight) {
    // 高小于标准
    rateH = Number((realHeight / normalHeight).toFixed(6));
  }
  rate = Math.min(rateH, rateW);
  if (rate === 0) rate = 1;
  initRate = rate;
  const calcVal = `${(Number(-((1 - rate) / 2).toFixed(6)) * 100).toFixed(4)}%`; // calc的值
  const calcAddVal = Number(
    ((realHeight - rate * normalHeight) / 2).toFixed(3)
  ); // calcl附加值
  styleStr =
    `width: ${normalWidth}px;height: ${normalHeight}px;` +
    `left: 50%;bottom: auto;right: auto;position: absolute;` +
    `transform: translate(-50%, calc(${calcVal}` +
    ` + ${calcAddVal}px)) scale(${rate})`;
  transformStr = ` translate(-50%, calc(${calcVal} + ${calcAddVal}px)) scale(${rate})`;
  return styleStr;
};
const getInitRate = (): number => {
  return initRate;
};
const getTransformStr = (): string => transformStr;

export default {
  getIframeTransform,
  setInitValue,
  getInitRate,
  setRoot,
  getTransformStr,
};
