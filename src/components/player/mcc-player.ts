import {
  InitParam,
  ItsMessage,
  IMccPlayer,
  ItsEvent,
  ItsPage,
  ItsStatus,
  ItsError,
  CWInitParams,
} from '../../interface';
import { CWService } from '../service/cw-service';
import {
  CheckResourceAvailableParam,
  SourceAvailableParam,
  VideoStateChangedParam,
} from '../course-bridge';
import handleDataPoints from '../../libs/handle-data-points';
import { isThinkAcademyPC } from '../../libs/utils';
import gamePreload from '../game/game-preload';
import { ParamsToCW } from '../course-bridge/course-type';
import {
  showGameEndTip,
  showGameStartTip,
  showAnswerTip,
} from '../../../src/libs/gameTip';
const GET_LATEST_MSG_TIMEOUT = 3000;
export default abstract class MccPlayer implements IMccPlayer {
  root: HTMLElement;
  params: InitParam;
  remoteUrlIndex: number;
  private isLocalUrl: boolean;
  getLatestMsgTimer: number;
  public cwService: CWService;
  public isGameing = false;
  public status: ItsStatus;
  public interactId: string;
  public answerTip: string;
  constructor(root: string | HTMLElement) {
    this.root = null;
    this.status = ItsStatus.Created;
    if (typeof root === 'string') {
      this.root = document.querySelector(root);
    } else if (root instanceof HTMLElement) {
      this.root = root;
    }
    if (!this.root) {
      throw Error('root is required ');
    }
  }
  private checkoutCWUrl(userLocal = true) {
    console.info(`[桥接层] checkoutCWUrl ${this.cwService.currentUrl}`);
    let url;
    if (this.params.localUrl && userLocal) {
      url = this.params.localUrl;
      this.isLocalUrl = true;
    } else {
      this.isLocalUrl = false;
      this.remoteUrlIndex++;
      this.remoteUrlIndex = this.remoteUrlIndex % this.params.remoteUrl.length;
      url = this.params.remoteUrl[this.remoteUrlIndex];
    }
    this.status = ItsStatus.Loading;
    handleDataPoints.startLoadCourseware(url, this.isLocalUrl);
    this.cwService.setCWUrl(url);
    if (!isThinkAcademyPC() && !url) {
      this.removeLoading();
      this.params.onEvent(ItsEvent.Error, {
        status: 'error',
        code: ItsError.URLError,
      });
    }
  }
  protected onCatelogChange(catelogInfo: ItsPage[]): boolean {
    if (this.status === ItsStatus.Ready) {
      console.info(`[桥接层] onCatelogChange ${this.cwService.currentUrl}`);
      this.params.onEvent(ItsEvent.CatalogueChange, { pages: catelogInfo });
      return true;
    }
    return false;
  }
  protected onPageChange(oldId: string, newId: string): boolean {
    console.info(`[桥接层] onPageChange oldId:${oldId},newId:${newId}`);
    this.params.onEvent(ItsEvent.CurrentPageChange, {
      oldId,
      newId,
    });
    handleDataPoints.setPageId(newId);
    return true;
  }

  // 监听翻页传递数据给教师端
  protected onPageChangeToTeacher(data: unknown): void {
    console.log('[桥接层]:onPageChangeToTeacher');
    this.params.onEvent(ItsEvent.PageChangeToTeacher, data);
  }
  protected onPageChanged(data: unknown): void {
    console.log('[桥接层]:onPageChanged');
    this.params.onEvent(ItsEvent.PageChanged, data);
  }
  protected onSendTestResult(data: unknown): void {
    console.log('[桥接层]:onSendTestResult');
    // 如果存在，则是手动提交，显示答案
    if (this.answerTip) {
      showAnswerTip(true, this.answerTip);
    }
    this.params.onEvent(ItsEvent.SendTestResult, data);
  }

  protected async recoverItsMessage(): Promise<boolean> {
    console.info('[桥接层] recoverItsMessage');
    const msg = (await this.getLatestItsMessage()) as ItsMessage;
    this.handleItsMessage(msg, true);
    return Boolean(msg && Object.keys(msg).length);
  }
  private getLatestItsMessage(): Promise<ItsMessage> {
    return new Promise((resolve) => {
      window.clearTimeout(this.getLatestMsgTimer);
      this.getLatestMsgTimer = window.setTimeout(() => {
        console.info(
          `[桥接层] getLatestItsMessage ${this.cwService.currentUrl}`
        );
        resolve({} as ItsMessage); // Update the resolve value to an empty object
      }, GET_LATEST_MSG_TIMEOUT);
      this.params.getLatestItsMessage((msg) => {
        console.info('[桥接层] getLatestItsMessage', msg);
        window.clearTimeout(this.getLatestMsgTimer);
        resolve(msg);
      });
    });
  }

  protected handleLoadedError(err: unknown): void {
    console.error(
      `[桥接层] handleLoadedError ${this.cwService.currentUrl} ${err}`
    );
    handleDataPoints.endLoadCourseware(true);
    if (!err) {
      err = { code: '没有报错信息' };
    }
    const errFormated = err as { code: string };
    const code = errFormated?.code;
    const isLocal = this.cwService.currentUrl === this.params.localUrl;
    if (isLocal) {
      if (code === ItsError.LoadError || code === ItsError.LoadTimeout) {
        this.params.onEvent(ItsEvent.Error, {
          code,
          url: this.cwService.currentUrl,
          isLocal,
          err,
        });
        this.checkoutCWUrl(false);
      } else {
        this.params.onEvent(ItsEvent.Error, {
          code,
          url: this.cwService.currentUrl,
          isLocal,
          err,
        });
      }
    } else {
      this.params.onEvent(ItsEvent.Error, {
        code,
        url: this.cwService.currentUrl,
        isLocal,
        err,
      });
    }
  }

  protected handleCWLoaded(): void {
    console.info(
      `[桥接层] handleCWLoaded ${this.cwService.currentUrl} ${ItsStatus.Loaded}`
    );
    this.status = ItsStatus.Loaded;
    const isLocal = this.cwService.currentUrl === this.params.localUrl;
    this.params.onEvent(ItsEvent.StatusChange, {
      status: ItsStatus.Loaded,
      url: this.cwService.currentUrl,
      isLocal,
    });
  }
  protected getCWStatus(): ItsMessage {
    return {
      itsId: this.params.itsId,
      currentIndex: this.cwService.currentPageIndex,
      pages: this.cwService.attachedPages,
      seq: undefined,
    };
  }
  protected abstract handleItsMessage(
    msg: ItsMessage,
    isLatestMsg?: boolean
  ): void;
  protected handleCWReady(): void {
    this.status = ItsStatus.Ready;
    this.params.onEvent(ItsEvent.StatusChange, {
      status: ItsStatus.Ready,
    });
    this.onCatelogChange(this.cwService.mergedPages);
    this.onPageChange(
      '',
      this.cwService.getPageIdByIndex(this.cwService.currentPageIndex)
    );
    // this.onPageChangeToTeacher(this);
    handleDataPoints.endLoadCourseware(false);
  }
  protected getCWServiceInitParam(params: InitParam): CWInitParams {
    return {
      width: params.screenWidth,
      height: params.screenHeight,
      role: params.role,
      itsId: params.itsId,
      showPagePercent: params.showPagePercent || 30,
      optimizationPageLoad: params.optimizationPageLoad || true,
      root: this.root,
      loadGame: params.loadGame,
      gamePreload: params.gamePreload,
      pageResourceList: params.pageResourceList,
      gameToast:
        params.gameToast ||
        (params.schoolCode == '85201'
          ? '請稍等老師演示結束'
          : `In the teacher's operation of the courseware, you can't play games yet~`),
      gameTip:
        params.gameTip ||
        (params.schoolCode == '85201' ? '老師演示中' : 'Teacher demonstrating'),
      tipGameStart: params.tipGameStart,
      tipGameEnd: params.tipGameEnd,
      rate: params.rate,
      bgColor: params.bgColor || 'transparent',
      onCatelogChange: this.onCatelogChange.bind(this),
      onPageChange: this.onPageChange.bind(this),
      onPageChangeToTeacher: this.onPageChangeToTeacher.bind(this),
      onSendTestResult: this.onSendTestResult.bind(this),
      onLoaded: this.handleCWLoaded.bind(this),
      onLoadError: this.handleLoadedError.bind(this),
      onCWLoadingProgress: (data: unknown) => {
        if (
          !isThinkAcademyPC() &&
          ((data['loaded'] / data['total']) * 100 >=
            params['showPagePercent'] ||
            data['loaded'] / data['total'] === 1)
        ) {
          this.removeLoading();
        }
        const isLocal = this.cwService.currentUrl === this.params.localUrl;
        data['isLocal'] = isLocal;
        this.params.onEvent(ItsEvent.LoadingProgress, data);
      },
      onReady: this.handleCWReady.bind(this),
      onCWCheckResource: this.handleCWChecnResource.bind(this),
      onMediaStateChange: (data: VideoStateChangedParam) => {
        this.params.onEvent(ItsEvent.MediaStatusChanged, data);
      },
      storeCWData: (data) => {
        return new Promise((resolve) => {
          try {
            this.params.storeData(data.key, data.value, (success) => {
              resolve(success);
            });
          } catch (e) {
            resolve(false);
          }
        });
      },
      getCWGetData: () => {
        return new Promise((resolve) => {
          try {
            this.params.getAllStoredData((data: unknown) => {
              resolve(data);
            });
          } catch (e) {
            resolve(null);
          }
        });
      },
      log: this.log.bind(this),
    };
  }
  private handleCWChecnResource(
    cwResourceList: CheckResourceAvailableParam
  ): void {
    const urls = cwResourceList.map((item) => item.url);
    this.params.checkResource(
      urls,
      (localResourceList: Array<{ path: string; exist: boolean }>) => {
        if (localResourceList.length == cwResourceList.length) {
          const result: SourceAvailableParam = [];
          for (let i = 0; i < localResourceList.length; i++) {
            const cwResourceItem = cwResourceList.find(
              (item) => item.url === localResourceList[i].path
            );
            if (cwResourceItem) {
              result.push({
                id: cwResourceItem.id,
                url: cwResourceItem.url,
                available: localResourceList[i].exist,
              });
            }
          }
          this.cwService.noticeCWResourceStatus(result);
        }
      }
    );
  }

  public init(params: InitParam): void {
    console.log('[桥接层] init', params);
    this.params = params;
    this.remoteUrlIndex = -1;
    handleDataPoints.setReportHandler(this.params.onEvent);
    handleDataPoints.setCWId(this.params.itsId);
    this.cwService = new CWService();
    const cwParams: CWInitParams = this.getCWServiceInitParam(params);
    this.cwService.init(cwParams);
    if (this.params.loadGame === '1') {
      if (this.params.pageResourceList.length > 0) {
        this.log('pageResourceList传入成功');
      } else {
        this.log('pageResourceList传入失败', 'error');
      }
      gamePreload.setPreloadInfo(params);
    }
    this.checkoutCWUrl();
  }
  public resizeCW(width: number, height: number): void {
    this.cwService.resizeCW(width, height);
  }
  public handleRoomItsMessage(msg: ItsMessage): void {
    // console.info('[桥接层] handleRoomItsMessage ', msg);
    if (this.params.loadGame === '1' && msg.origin === 'game') {
      console.info('[gamePreload] 接收handleRoomItsMessage ', msg);
      if (msg.cwAction.type === 'Restart') {
        this.isGameing = true;
        gamePreload.receiveTransferMessage(msg.cwAction);
        return;
      }
      if (msg.cwAction.type === 'closeGame') {
        this.isGameing = false;
      }
      if (this.isGameing) {
        console.log('[桥接层]游戏中不接收课件任何消息');
        return;
      }
      if (msg.currentIndex == this.cwService.currentPageIndex) {
        gamePreload.receiveTransferMessage(msg.cwAction);
      }
    } else {
      if (this.isGameing || msg.origin === 'game') {
        console.info(
          '[桥接层]游戏中不接收课件任何消息或者不支持预加载的时候收到游戏的信息 this.isGameing:',
          this.isGameing
        );
        return;
      }
      this.handleItsMessage(msg);
    }
  }
  public handleReconnected(): void {
    if (this.status === ItsStatus.Ready) {
      this.recoverItsMessage();
      this.cwService.noticeGetCWState();
    }
  }
  public log(data: any, level = 'info'): void {
    console.info('save log', data, level);
    // 如果data是字符串,变成对象
    if (typeof data === 'string') {
      data = {
        msg: data,
      };
    }
    this.params.onEvent &&
      this.params.onEvent(ItsEvent.Console, {
        ...data,
        level,
      });
  }
  public reloadIts(): void {
    this.checkoutCWUrl();
  }
  private removeLoading() {
    const loadingDom = document.getElementById('loading');
    loadingDom && this.root.removeChild(loadingDom);
  }
  public handleMessageToCourseware(msg: ParamsToCW): void {
    if (msg.type === 'beginTest') {
      showGameStartTip();
      this.interactId = msg.interactId;
      this.answerTip = msg.answerTip + msg.correctAnswer;
    } else if (msg.type === 'endTest') {
      this.answerTip = '';
      showAnswerTip(false);
      showGameEndTip();
    }
    this.cwService.sendMessageToCourseware(msg);
  }
  destroy(): void {
    console.info('[桥接层] destroy销毁');
    this.cwService.unbindEvent();
  }
}
