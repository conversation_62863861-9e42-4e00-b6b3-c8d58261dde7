import MccPlayer from './mcc-player';
import { ItsStatus, ItsMessage } from '../../interface';
export default class StudentMccPlayer extends MccPlayer {
  private seq = 0;
  protected async handleCWLoaded(): Promise<void> {
    super.handleCWLoaded();
    // if (!(await this.recoverItsMessage())) {
    //   this.cwService.pageTo(0);
    // }
  }
  private checkItsMessage(msg: ItsMessage, isLatestMsg: boolean): boolean {
    if (!msg || !Object.keys(msg).length) {
      return false;
    }
    if (this.seq === msg.seq) {
      return false;
    }
    // if (!isLatestMsg && this.status != ItsStatus.Ready) {
    //   return false;
    // }
    // if (this.status != ItsStatus.Ready) {
    //   console.log(this.status, ItsStatus.Ready, 'msg.seq');

    //   return false;
    // }
    return true;
  }
  private handleCwAction(cwAction: unknown) {
    if (cwAction) {
      this.cwService?.receiveTransferMessage(cwAction);
    }
  }
  protected handleItsMessage(msg: ItsMessage, isLatestMsg = false): void {
    // console.log(isLatestMsg, 'isLatestMsg');
    // // console.log(msg.seq, 'msg.seq');

    if (this.checkItsMessage(msg, isLatestMsg)) {
      const { currentIndex, pages, cwAction, seq } = msg;
      this.seq = seq;
      this.cwService.setAttachedPage(pages);

      if (this.cwService.currentPageIndex != currentIndex) {
        if (currentIndex < 0) {
          console.info('[桥接层] 桥接层收到的消息pageIndex与当前页不一致，翻到第一页')
          return this.cwService.pageTo(0);
        }
        console.info('[桥接层] 桥接层收到的消息pageIndex与当前页不一致，翻到消息的pageIndex')
        this.cwService.pageTo(currentIndex);
      }
      if (!isLatestMsg) {
        this.handleCwAction(cwAction);
      }
    }
  }
}
