import MccPlayer from './mcc-player';
import {
  ItsMessage,
  ITeacherMccPlayer,
  ItsPage,
  InitParam,
  CWInitParams,
  ItsPageType,
} from '../../interface';
import ReportService from '../service/report-service';
import { NeedPageChangeDir } from '../course-bridge';

export default class TeacherMccPlayer
  extends MccPlayer
  implements ITeacherMccPlayer
{
  private checkItsMessage(msg: ItsMessage, isLatestMsg: boolean): boolean {
    if (!msg || !Object.keys(msg).length) {
      return false;
    }
    if (this.seq === msg.seq) {
      return false;
    }
    // if (!isLatestMsg && this.status != ItsStatus.Ready) {
    //   return false;
    // }
    // if (this.status != ItsStatus.Ready) {
    //   console.log(this.status, ItsStatus.Ready, 'msg.seq');

    //   return false;
    // }
    return true;
  }
  protected handleItsMessage(msg: ItsMessage, isLatestMsg = false): void {
    // console.log(isLatestMsg, 'isLatestMsg');
    // // console.log(msg.seq, 'msg.seq');
    // console.log(msg, 'latestMsg');
    console.log('[桥阶层] handleItsMessage', msg, isLatestMsg);
    if (this.checkItsMessage(msg, isLatestMsg)) {
      const { currentIndex, pages, cwAction, seq } = msg;
      this.seq = seq;
      this.cwService.setAttachedPage(pages);

      if (this.cwService.currentPageIndex != currentIndex) {
        if (currentIndex < 0) {
          return this.cwService.pageTo(0);
        }
        this.cwService.pageTo(currentIndex);
      }
      if (!isLatestMsg) {
        this.handleCwAction(cwAction);
      }
    }
  }
  private handleCwAction(cwAction: unknown) {
    if (cwAction) {
      this.cwService?.receiveTransferMessage(cwAction);
    }
  }
  public reportService: ReportService;
  private seq = 0;
  protected handleCWLoaded(): void {
    super.handleCWLoaded();
    console.log('[桥接层] 主讲端handleCWLoaded');
    setTimeout(() => {
      console.info('[桥接层] 主讲端初始化,等2秒后翻页');
      this.cwService.pageTo(-1);
    }, 2000);
  }
  protected handleCWReady(): void {
    super.handleCWReady();
    // this.reportService.startSyncMessage();
  }

  protected onCatelogChange(catelogInfo: ItsPage[]): boolean {
    if (super.onCatelogChange(catelogInfo)) {
      // this.reportService.reportItsMessageNow();
      return true;
    }
    return false;
  }
  protected getCWServiceInitParam(params: InitParam): CWInitParams {
    const cwParams = super.getCWServiceInitParam(params);
    cwParams.onCWSendMessage = (cwAction: any) => {
      const data = this.getCWStatus();
      if (
        cwAction.type === 'noticeStudentSyncCWState' &&
        cwAction.data.pageIndex !== data.currentIndex
      )
        return;
      data.cwAction = cwAction;
      data.origin = 'CW';

      this.reportService.reportItsMessageNow(data);
    };
    // 这个是为了屏蔽点击屏幕翻页
    // cwParams.onCWSendNeedChange = this.handleSendNeedChangePage.bind(this);
    return cwParams;
  }
  private handleSendNeedChangePage(dir: NeedPageChangeDir) {
    if (dir === NeedPageChangeDir.Next) {
      this.cwService.pageDown();
    } else if (dir === NeedPageChangeDir.Prev) {
      this.cwService.pageUp();
    }
  }
  protected onPageChange(oldId: string, newId: string): boolean {
    if (super.onPageChange(oldId, newId)) {
      // this.reportService.reportItsMessageNow();
      return true;
    }
    return false;
  }

  public init(params: InitParam): void {
    super.init(params);
    this.reportService = new ReportService(
      this.params.sendRoomItsMessage,
      this.getCWStatus.bind(this)
    );
  }
  pageDown(): void {
    this.cwService.pageDown();
  }
  pageUp(): void {
    this.cwService.pageUp();
  }
  pageTo(pageId: string): void {
    console.info('[桥接层] 主讲端翻页到:', pageId);
    const index = this.cwService.getPageIndexByPageId(pageId);
    console.info('[桥接层] 主讲端翻页到index:', index);
    this.cwService.pageTo(index);
  }
  animationDown(): void {
    this.cwService.animationDown();
  }
  animationUp(): void {
    this.cwService.animationUp();
  }

  setElementOperate(isDrag: boolean): void {
    this.cwService.setElementOperate(isDrag);
  }

  insertPage(
    pageId: string,
    isStay = true,
    imgUrl = '',
    type = ItsPageType.Blank
  ): void {
    const index = this.cwService.getPageIndexByPageId(pageId);
    this.cwService.addPage(index, isStay, type, imgUrl);
  }
  removePage(): void {
    this.cwService.removePage();
  }
}
