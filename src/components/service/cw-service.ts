import {
  Role,
  ItsPage,
  ItsPageType,
  CWInitParams,
  ItsEvent,
  ItsError,
} from '../../interface';
import CourseBridge from '../course-bridge';
import {
  CoursewareEvent,
  GetCatalogueInfoParam,
  HyperlinksMessageParam,
  SourceAvailableParam,
} from '../course-bridge';
import { v4 as uuid } from 'uuid';
import cloneDeep from 'lodash/cloneDeep';
import { isThinkAcademyPC } from '../../libs/utils';
import gamePreload from '../game/game-preload';
import { showGameDemoTip } from '../../../src/libs/gameTip';
import { ParamsToCW } from '../course-bridge/course-type';
export class CWService {
  private options: CWInitParams;
  private cwDom: HTMLIFrameElement;
  private blankPageDom: HTMLElement;
  private loadingDom: HTMLElement;
  private gameDemoTipDom: HTMLElement;
  private gameDemoToastDom: HTMLElement;
  private answerTipDom: HTMLElement;
  private gameStartTipDom: HTMLElement;
  private gameEndTipDom: HTMLElement;
  private courseBridge: CourseBridge;

  public currentUrl = '';
  public currentPageIndex = -1;
  public currentPageIsGame = false;
  public cwPages: ItsPage[];
  public attachedPages: ItsPage[] = [];
  public mergedPages: ItsPage[] = [];
  public imgUrl = '';
  public init(options: CWInitParams): void {
    console.info('[桥接层]cw init', options);
    this.options = options;
    this.createCWDom();
    if (this.options.role === 'student') {
      this.createGameDemoTip(this.options.gameTip);
      this.createGameDemoToast(this.options.gameToast);
      this.createGameEndTip();
      this.createGameStartTip();
      this.createAnswerTip();
    }
    !isThinkAcademyPC() && this.createLoadingDom();
    this.options.root && this.mount(this.options.root);
    this.initBridge();
    if (this.options.root && this.options.loadGame === '1') {
      gamePreload.createIframeContainer(this.options.root);
      gamePreload.sendRoomItsMessage = this.options.onCWSendMessage;
    }
  }
  private handleTransferMessage(data: { groupId?: string; data?: any }) {
    if (this.currentPageIsGame || this.currentPageIndex < 0) {
      console.info('[桥接层] 当前页是游戏页,主讲端课件不上报同步消息');
      return;
    }
    // if (data.groupId) {
    //   if (!data.data.isHeartBreak) {
    //     console.info('[桥接层] 新游戏点击', data);
    //     this.receiveTransferMessage(data);
    //   }
    // }
    // 如果是课件内游戏,需要把操作传回去
    this.options.onCWSendMessage && this.options.onCWSendMessage(data);
  }
  private initBridge() {
    this.courseBridge = new CourseBridge(this.cwDom);
    this.options.onCWSendMessage &&
      this.courseBridge.on(
        CoursewareEvent.TransferMessageSend,
        this.handleTransferMessage.bind(this)
      );
    this.courseBridge.on(CoursewareEvent.Ready, this.options.onReady);
    this.options.storeCWData &&
      this.courseBridge.on(
        CoursewareEvent.StoreCWState,
        this.options.storeCWData
      );
    this.courseBridge.on(
      CoursewareEvent.GetCWState,
      this.handleGetCWState.bind(this)
    );
    this.options.onCWLoadingProgress &&
      this.courseBridge.on(
        CoursewareEvent.CoursewareLoadingProgress,
        this.options.onCWLoadingProgress
      );
    this.courseBridge.on(
      CoursewareEvent.CheckSourceAvailable,
      this.options.onCWCheckResource
    );
    this.options.onMediaStateChange &&
      this.courseBridge.on(
        CoursewareEvent.MediaStateChanged,
        this.options.onMediaStateChange
      );
    this.options.onCWSendNeedChange &&
      this.courseBridge.on(
        CoursewareEvent.SendNeedChangePage,
        this.options.onCWSendNeedChange
      );
    this.courseBridge.on(
      CoursewareEvent.HyperlinksMessage,
      this.handleHyperlinksMessage.bind(this)
    );

    // 监听翻页传递数据给教师端
    this.options.onPageChangeToTeacher &&
      this.courseBridge.on(
        CoursewareEvent.PageChangeToTeacher,
        this.options.onPageChangeToTeacher
      );
    // 监听判题消息
    this.options.onSendTestResult &&
      this.courseBridge.on(
        CoursewareEvent.SendTestResult,
        this.options.onSendTestResult
      );
  }
  public noticeCWResourceStatus(resourceItems: SourceAvailableParam): void {
    this.courseBridge.sourceAvailable(resourceItems);
  }
  private async handleGetCWState() {
    const data = await this.options.getCWGetData();
    if (data) {
      this.courseBridge.recoverCWState(data);
    }
  }
  private handleHyperlinksMessage(data: HyperlinksMessageParam) {
    // console.info('[桥接层] handleHyperlinksMessage', data);
    const pageIndex = this.getPageIndexByPageId(data.targetPageId);
    if (pageIndex >= 0 && this.currentPageIndex != pageIndex) {
      this.pageTo(pageIndex);
    }
  }
  private handleCatalogueGetted(pagesInfo: GetCatalogueInfoParam) {
    // console.info('[桥接层] handleCatalogueGetted', pagesInfo);
    if (pagesInfo) {
      let pageData = pagesInfo.filter((item) => item.type === 'item');
      // 设置初始化宽高
      this.cwPages = pageData.map((pageItem, index) => {
        const { item } = pageItem;
        return {
          pageId: item.id,
          type: ItsPageType.Courseware,
          title: item.title,
          isHide: item.isHide,
          note: item.note,
          index,
        };
      });
      this.setMergedPages(cloneDeep(this.cwPages));
      if (this.options.loadGame == '1') {
        pageData = pageData.map((pageItem) => {
          const { item } = pageItem;
          if (item.isHide) return pageItem;
          const pageResource = this.options.pageResourceList.find(
            (page) => page.pageId === item.id
          );
          return {
            ...pageItem,
            isGame: pageResource.gameLocalPath && pageResource.resType === 1,
            isPrereload: pageResource.gameIsPreReload,
            supportKeepAndRestart: pageResource.gameIsPlay,
            titleId: pageResource.gameTitleId,
            gameId: pageResource.gameId, // 游戏id， item.gameId
            coursewareId: pageResource.gameCoursewareId, //
            gameIsNewPlatform: pageResource.gameIsNewPlatform,
          };
        });
        gamePreload
          .getPageGameInfo(pageData, false)
          .then((res) => {
            // 新目录排序
            // console.info('[gamePreload]新目录排序', res);
            // let additionalCatelogs = res[0];
            // additionalCatelogs = additionalCatelogs.sort(
            //   (pre, next) => pre.index - next.index
            // );
            // gamePreload.addtionalCatelogs = additionalCatelogs; // 暂存
            // console.info('游戏目录', additionalCatelogs);
          })
          .catch(() => {
            if (gamePreload.initParams.role === Role.Teacher) {
              console.error('数据拆分----请求新目录失败----课件不上报ready');
              gamePreload.initParams.onEvent(ItsEvent.Error, {
                code: ItsError.LoadError,
                err: '游戏请求新目录失败',
              });
            }
          });
      }
    }
  }

  private createCWDom(): void {
    const iframe = document.createElement('iframe');
    const width =
      typeof this.options.width === 'number'
        ? this.options.width + 'px'
        : this.options.width;
    const height =
      typeof this.options.height === 'number'
        ? this.options.height + 'px'
        : this.options.height;
    iframe.width = width;
    iframe.height = height;
    iframe.frameBorder = '0';
    iframe.scrolling = 'no';
    iframe.allowFullscreen = true;
    iframe.setAttribute('allow', 'autoplay');
    this.cwDom = iframe;

    const blankPage = document.createElement('div');
    blankPage.style.width = width;
    blankPage.style.height = height;
    blankPage.style.float = 'left';
    blankPage.style.backgroundColor = 'rgba(36, 59, 58)';
    blankPage.style.backgroundSize = '100% 100%';
    this.blankPageDom = blankPage;
    this.hideBlankPage();

    this.setCWUrl(this.options.url);
  }
  public getPageIndexByPageId(id: string): number {
    return this.mergedPages.findIndex((item) => item.pageId === id);
  }
  public showBlankPage(): void {
    this.blankPageDom.style.display = 'block';
  }
  public hideBlankPage(): void {
    this.blankPageDom.style.display = 'none';
  }
  public noticeGetCWState(): void {
    this.courseBridge.noticeGetCWState();
  }
  public resizeCW(width: number, height: number): void {
    this.blankPageDom.style.width = width + 'px';
    this.blankPageDom.style.height = height + 'px';
    this.cwDom.width = width + 'px';
    this.cwDom.height = height + 'px';
  }
  public setCWUrl(url: string): void {
    console.info('[桥接层] setCWUrl', url);
    if (url) {
      this.currentUrl = url;
      const urlObj = new URL(url);
      const sp = urlObj.searchParams;
      const map = {
        id: this.options.itsId,
        line: 'off',
        env: 31,
        devAutoChangePage: false,
        optimizationPageLoad: this.options.optimizationPageLoad || true,
        // autoChangePage: false,
        role: this.options.role === Role.Teacher ? 'teacher' : 'student',
        omoType: 'online', // 仅显示线上相关的课件页
        previewType: 'h5',
      };
      for (const key in map) {
        sp.set(key, map[key]);
      }
      this.options.log(`桥接层地址${urlObj.toString()}`);
      if (this.options.loadGame === '1') {
        gamePreload.setCourseWareUrl(urlObj.toString());
      }

      this.courseBridge
        .load(urlObj.toString(), this.options.role)
        .then(async () => {
          console.info('[桥接层]加载桥接层成功');
          this.courseBridge.setUpBodyBackground(this.options.bgColor);
          try {
            const cwPages =
              (await this.courseBridge.getCatalogueInfo()) as GetCatalogueInfoParam;
            console.info('[桥接层]获取课件目录成功', cwPages);
            this.handleCatalogueGetted(cwPages);
          } catch (err) {
            console.error('[桥接层]获取课件目录失败', err);
            this.options.onLoadError({
              code: ItsError.LoadTimeout,
              err: '获取课件目录失败',
            });
          }

          this.options?.onLoaded();
        })
        .catch((err) => {
          console.error('[桥接层]加载桥接层失败', err);
          this.options.onLoadError({
            code: ItsError.LoadError,
            err: '加载桥接层失败',
          });
        });
      console.log(urlObj.toString(), 'urlObj.toString()');
    }
  }
  public receiveTransferMessage(cwAction: unknown): void {
    if (this.currentPageIsGame) {
      gamePreload.receiveTransferMessage(cwAction);
    } else {
      this.courseBridge?.receiveTransferMessage(cwAction);
    }
  }
  public sendMessageToCourseware(cwAction: ParamsToCW): void {
    if (cwAction) this.courseBridge?.sendMessageToCourseware(cwAction);
  }
  public setAttachPage(pages: ItsPage[]): void {
    this.attachedPages = pages;
  }
  public pageDown(): void {
    if (this.cwPages) {
      let i;
      for (i = this.currentPageIndex + 1; i < this.mergedPages.length; i++) {
        if (!this.mergedPages[i].isHide) {
          break;
        }
      }
      if (i >= 0 && i < this.mergedPages.length) {
        this.pageTo(i);
      }
    }
  }
  public pageUp(): void {
    if (this.cwPages) {
      let i;
      for (i = this.currentPageIndex - 1; i >= 0; i--) {
        if (!this.mergedPages[i].isHide) {
          break;
        }
      }
      if (i >= 0 && i < this.mergedPages.length) {
        this.pageTo(i);
      }
    }
  }
  public animationDown(): void {
    this.courseBridge.animationDown();
  }
  public animationUp(): void {
    this.courseBridge.animationUp();
  }
  public setElementOperate(isDrag: boolean): void {
    this.courseBridge.setElementOperate(isDrag);
  }
  public pageTo(index: number): void {
    console.info('[桥接层] cw pageTo', index);
    if (index < 0 && this.currentPageIndex >= 0) {
      console.info('[桥接层] 当前已经历史恢复了,不需要翻到0页');
      return;
    }
    if (index < 0) {
      console.warn('[桥接层] 翻页数不能小于0');
      index = 0;
    }
    if (index >= 0 && index < this.mergedPages.length) {
      const oldIndex = this.currentPageIndex;
      const currentPage = this.mergedPages[index];
      if (currentPage) {
        this.currentPageIndex = index;
        this.options?.onPageChange(
          this.getPageIdByIndex(oldIndex),
          this.getPageIdByIndex(index)
        );
        // this.cwPageTo(currentPage?.index);
        if (currentPage.type === ItsPageType.Courseware) {
          this.hideBlankPage();
          this.cwPageTo(currentPage?.index);
        } else {
          this.showBlankPage();
        }
      }
    }
  }
  private cwPageTo(index: number) {
    console.info('[打点][桥接层] cwPageTo', index);
    if (index >= 0) {
      if (this.options.loadGame === '1') {
        gamePreload.closeAllIframe(index);
        console.info('[gamePreload]需要预加载游戏');
        // 不需要预加载游戏
        if (gamePreload.pageIsGame(index)) {
          // 当前是游戏
          console.info('[gamePreload]修改this.currentPageIsGame1', index);
          this.currentPageIsGame = true;
          console.info('[gamePreload]当前是游戏', index);
          gamePreload.showIframe(index);
          console.log(
            '打点 this.mergedPages[index].pageId',
            this.mergedPages[index].pageId,
            '翻页:',
            index
          );
          this.options.role === 'student' && showGameDemoTip(true);
          this.options.onPageChangeToTeacher &&
            this.options.onPageChangeToTeacher({
              currentPageData: {
                id: this.mergedPages[index].pageId,
                title: '',
              },
              pageIndex: index,
              interactiveTemplatePath: true,
              includeInteractiveTemplate: true,
            });
          this.courseBridge.pageTurning('virtual').catch((err) => {
            console.warn('page turning error', err);
          });
        } else {
          // 不是游戏，也要更新缓存
          this.options.role === 'student' && showGameDemoTip(false);
          console.info('[gamePreload]当前不是游戏', index);
          console.info('[gamePreload]修改this.currentPageIsGame2', index);
          this.currentPageIsGame = false;
          this.courseBridge.pageTurning(index).catch((err) => {
            console.warn('page turning error', err);
          });
          gamePreload.waitToStoregaGame(index, null, false);
        }
      } else {
        console.info('[gamePreload]不需要预加载游戏');
        console.info('[gamePreload]修改this.currentPageIsGame3', index);
        this.currentPageIsGame = false;
        this.courseBridge.pageTurning(index).catch((err) => {
          console.warn('page turning error', err);
        });
      }
    }
  }

  public mount(root: HTMLElement): void {
    if (!root) {
      throw Error('root element is required');
    }
    root.innerHTML = '';
    root.appendChild(this.blankPageDom);
    root.appendChild(this.cwDom);
    !isThinkAcademyPC() && root.appendChild(this.loadingDom);
    if (this.options.role === 'student') {
      root.appendChild(this.answerTipDom);
      root.appendChild(this.gameDemoTipDom);
      root.appendChild(this.gameDemoToastDom);
      root.appendChild(this.gameEndTipDom);
      root.appendChild(this.gameStartTipDom);
    }
  }
  public getPageIdByIndex(index: number): string {
    if (!this.cwPages || index < 0) return;
    return this.cwPages[index]?.pageId;
  }
  private isAttachedPagesChanged(attachedPages: ItsPage[]): boolean {
    // console.info('[桥接层] isAttachedPagesChanged', attachedPages);
    const newPages = attachedPages;
    const oldPages = this.attachedPages;
    if (oldPages.length == newPages.length) {
      for (let i = 0; i < oldPages.length; i++) {
        if (oldPages[i].pageId !== newPages[i].pageId) {
          return true;
        }
      }
      return false;
    }
    return true;
  }
  private setMergedPages(pages) {
    // console.info('[桥接层] setMergedPages', pages);
    this.mergedPages = pages;
    this.options.onCatelogChange(pages);
  }
  public setAttachedPage(attachedPages: ItsPage[]): void {
    // console.info('[桥接层] setAttachedPage', attachedPages);
    if (this.isAttachedPagesChanged(attachedPages)) {
      this.attachedPages = attachedPages;
      const pages = new Array(this.cwPages.length + attachedPages.length);
      for (let i = 0; i < attachedPages.length; i++) {
        pages[attachedPages[i].index] = attachedPages[i];
      }
      for (let i = 0, k = 0; i < pages.length; i++) {
        pages[i] = pages[i] || this.cwPages[k++];
      }
      this.setMergedPages(pages);
    }
  }
  public addPage(
    pageIndex: number,
    isStay: boolean,
    type = ItsPageType.Blank,
    imgUrl: string
  ): void {
    console.info('[桥接层] addPage', pageIndex, isStay, type, imgUrl);
    if (imgUrl) {
      this.blankPageDom.innerHTML = `<img src="${imgUrl}" style="width:100%;heigth:100%;display:block" />`;
    }
    const attachedPages = [];
    this.mergedPages.splice(pageIndex + 1, 0, {
      isHide: 0,
      type,
      pageId: uuid(),
    });
    for (let i = 0; i < this.mergedPages.length; i++) {
      const item = this.mergedPages[i];
      if (item.type != ItsPageType.Courseware) {
        attachedPages.push({
          type: item.type,
          pageId: item.pageId,
          index: i,
        });
      }
    }
    this.attachedPages = attachedPages;

    this.options.onCatelogChange(this.mergedPages);
    const newPageIndex = pageIndex + 1;
    if (isStay) {
      if (newPageIndex == 0) {
        this.pageTo(this.currentPageIndex + 1);
      }
    } else {
      this.pageTo(newPageIndex);
    }
  }
  public removePage(): void {
    this.hideBlankPage();
    this.blankPageDom.innerHTML = '';
    this.pageUp();
  }

  public setOriginLocation(url: string): void {
    this.courseBridge.setOriginLocation(url);
  }
  public setIsLocationCW(useLocal: boolean): void {
    this.courseBridge.setIsLocationCW(useLocal);
  }

  private createLoadingDom(): void {
    const loadingDom = document.createElement('div');
    loadingDom.className = 'loading';
    loadingDom.setAttribute('id', 'loading');
    const loadingContenter = document.createElement('div');
    loadingContenter.className = 'loading-contenter';
    const loadingLogo = document.createElement('div');
    loadingLogo.className = 'loading-logo';
    const loadingAnimation = document.createElement('div');
    loadingAnimation.className = 'loading-animation';
    loadingContenter.append(loadingAnimation);
    loadingContenter.append(loadingLogo);
    loadingDom.append(loadingContenter);

    this.loadingDom = loadingDom;
  }

  /**
   * 创建游戏演示tip
   * @param text
   */
  private createGameDemoTip(text: string): void {
    const gameDemoTipDom = document.createElement('div');
    gameDemoTipDom.className = 'gameDemoTip';
    gameDemoTipDom.setAttribute('id', 'gameDemoTip');
    gameDemoTipDom.innerText = text;
    this.gameDemoTipDom = gameDemoTipDom;
  }

  /**
   * 创建游戏演示toast
   * @param text
   */
  private createGameDemoToast(text: string): void {
    const gameDemoToastDom = document.createElement('div');
    gameDemoToastDom.className = 'gameDemoToast';
    gameDemoToastDom.setAttribute('id', 'gameDemoToast');
    gameDemoToastDom.innerText = text;
    this.gameDemoToastDom = gameDemoToastDom;
  }
  private createAnswerTip(): void {
    const answerTipDom = document.createElement('div');
    answerTipDom.className = 'answerTip';
    answerTipDom.setAttribute('id', 'answerTip');
    this.answerTipDom = answerTipDom;
  }
  /**
   * 创建游戏开始tip
   */
  public createGameStartTip(): void {
    const gameStartTipDom = document.createElement('div');
    gameStartTipDom.className = 'gameStartTip';
    gameStartTipDom.setAttribute('id', 'gameStartTip');

    const bannerDom = document.createElement('div');
    bannerDom.className = 'banner';
    const textDom = document.createElement('div');
    textDom.className = 'text';
    textDom.innerText = this.options.tipGameStart;
    bannerDom.appendChild(textDom);
    const sunline1Dom = document.createElement('div');
    sunline1Dom.className = 'sunline1';
    bannerDom.appendChild(sunline1Dom);
    const sunline2Dom = document.createElement('div');
    sunline2Dom.className = 'sunline2';
    bannerDom.appendChild(sunline2Dom);
    gameStartTipDom.appendChild(bannerDom);
    const streamerDom = document.createElement('div');
    streamerDom.className = 'streamer';
    gameStartTipDom.appendChild(streamerDom);
    const upDom = document.createElement('div');
    if (this.options.rate == '2') {
      upDom.className = 'up rate2';
    } else {
      upDom.className = 'up';
    }
    gameStartTipDom.appendChild(upDom);

    this.gameStartTipDom = gameStartTipDom;

    this.gameStartTipDom.style.display = 'none';
  }
  /**
   * 创建游戏结束tip
   */
  public createGameEndTip(): void {
    const gameEndTipDom = document.createElement('div');
    gameEndTipDom.className = 'gameEndTip';
    gameEndTipDom.setAttribute('id', 'gameEndTip');

    const bannerDom = document.createElement('div');
    bannerDom.className = 'banner';
    const textDom = document.createElement('div');
    textDom.className = 'text';
    textDom.innerText = this.options.tipGameEnd;
    bannerDom.appendChild(textDom);
    const sunline1Dom = document.createElement('div');
    sunline1Dom.className = 'sunline1';
    bannerDom.appendChild(sunline1Dom);
    const sunline2Dom = document.createElement('div');
    sunline2Dom.className = 'sunline2';
    bannerDom.appendChild(sunline2Dom);
    gameEndTipDom.appendChild(bannerDom);
    const streamerDom = document.createElement('div');
    streamerDom.className = 'streamer';
    gameEndTipDom.appendChild(streamerDom);
    const upDom = document.createElement('div');
    if (this.options.rate == '2') {
      upDom.className = 'up rate2';
    } else {
      upDom.className = 'up';
    }
    gameEndTipDom.appendChild(upDom);
    this.gameEndTipDom = gameEndTipDom;

    this.gameEndTipDom.style.display = 'none';
  }
  unbindEvent(): void {
    this.options.onCWSendMessage &&
      this.courseBridge.removeListener(
        CoursewareEvent.TransferMessageSend,
        this.options.onCWSendMessage
      );
    this.courseBridge.removeListener(
      CoursewareEvent.Ready,
      this.options.onReady
    );
    this.options.storeCWData &&
      this.courseBridge.removeListener(
        CoursewareEvent.StoreCWState,
        this.options.storeCWData
      );
    this.courseBridge.removeListener(
      CoursewareEvent.GetCWState,
      this.handleGetCWState.bind(this)
    );
    this.options.onCWLoadingProgress &&
      this.courseBridge.removeListener(
        CoursewareEvent.CoursewareLoadingProgress,
        this.options.onCWLoadingProgress
      );
    this.courseBridge.removeListener(
      CoursewareEvent.CheckSourceAvailable,
      this.options.onCWCheckResource
    );
    this.options.onMediaStateChange &&
      this.courseBridge.removeListener(
        CoursewareEvent.MediaStateChanged,
        this.options.onMediaStateChange
      );
    this.options.onCWSendNeedChange &&
      this.courseBridge.removeListener(
        CoursewareEvent.SendNeedChangePage,
        this.options.onCWSendNeedChange
      );
    this.courseBridge.removeListener(
      CoursewareEvent.HyperlinksMessage,
      this.handleHyperlinksMessage.bind(this)
    );

    // 监听翻页传递数据给教师端
    this.options.onPageChangeToTeacher &&
      this.courseBridge.removeListener(
        CoursewareEvent.PageChangeToTeacher,
        this.options.onPageChangeToTeacher
      );

    // 调用 courseBridge 的 destroy 方法进行完整清理
    if (this.courseBridge && typeof this.courseBridge.destroy === 'function') {
      this.courseBridge.destroy();
    }

    gamePreload.destroy();
  }
}
