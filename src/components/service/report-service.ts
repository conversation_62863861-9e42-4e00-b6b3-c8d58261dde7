import { ItsMessage } from '../../interface';
import throttle from 'lodash/throttle';
export default class ReportService {
  private reportSeq: number;
  private reportItsMessageTrt: any;
  private syncTimer: number;
  static SYNC_INTERVAL = 1500;
  constructor(
    private reportHandle: (ItsMessage) => void,
    private getStatus: () => ItsMessage
  ) {
    this.reportSeq = Date.now();
    this.reportItsMessageTrt = throttle(
      this.doReportItsMessage.bind(this),
      3000
    );
  }
  private doReportItsMessage(data: ItsMessage): void {
    data.seq = this.reportSeq++;
    this.reportHandle(data);
  }
  public reportItsMessageNow(data?: ItsMessage): void {
    this.reportItsMessageTrt?.cancel();
    this.reportItsMessage(data);
  }
  public reportItsMessage(data?: ItsMessage): void {
    data = data || this.getStatus();
    this.reportItsMessageTrt(data);
  }
  public startSyncMessage(): void {
    if (this.syncTimer) {
      window.clearInterval(this.syncTimer);
    }
    this.syncTimer = window.setInterval(() => {
      const data = this.getStatus();
      data.origin = 'autoSync';
      this.reportItsMessage(data);
    }, ReportService.SYNC_INTERVAL);
  }
  public stopSyncMessage(): void {
    this.syncTimer && window.clearInterval(this.syncTimer);
  }
}
