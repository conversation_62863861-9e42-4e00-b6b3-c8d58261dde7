import TeacherMccPlayer from './components/player/teacher-mcc-player';
import StudentMccPlayer from './components/player/student-mcc-player';
export * from './interface';

type PlayerName = 'student' | 'teacher';

type PlayerType<T> = T extends 'student'
  ? StudentMccPlayer
  : T extends 'teacher'
  ? TeacherMccPlayer
  : never;
export default class MccPlayerFactory {
  updateTime: string;
  version: string;
  constructor() {
    this.updateTime = '__UPDATE_TIME__';
    this.version = '__VERSION__';
  }
  static getPlayer<T extends PlayerName>(
    role: T,
    root: HTMLElement | string
  ): PlayerType<T> {
    if (role === 'teacher') {
      return new TeacherMccPlayer(root) as PlayerType<T>;
    } else if (role === 'student') {
      return new StudentMccPlayer(root) as PlayerType<T>;
    } else {
      throw Error('not supported role:' + role);
    }
  }
}
