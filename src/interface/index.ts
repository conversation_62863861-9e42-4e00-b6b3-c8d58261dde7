import {
  CheckResourceAvailableParam,
  NeedPageChangeDir,
  StoreCWStateParam,
  VideoStateChangedParam,
} from '../components/course-bridge';

export enum ItsPageType {
  Courseware = 'course',
  Blank = 'blank',
}

export enum Role {
  Student = 'student',
  Teacher = 'teacher',
}
export interface ItsPage {
  type: string; // course, blank, pdf?
  pageId: string;
  index?: number;
  isHide: number; // TODO use boolean
  note?: string;
  title?: string;
}

export interface ItsMessage {
  seq: number; // 同步序列
  itsId: string; // 课件ID
  currentIndex: number; // 课件当前页，按索引
  pages: ItsPage[]; // 所有课件页
  cwAction?: { type?: string }; // 课件内部同步用
  origin?: string;
}

export enum ItsError {
  LoadError = 'E_LOAD_ERROR',
  LoadTimeout = 'E_LOAD_TIMEOUT',
  URLError = 'E_URL_ERROR',
  CatalogueInfo = 'E_Catalogue_TIMEOUT',
  BoundingClientRectByItemId = 'E_BoundingClientRectByItemId_TIMEOUT',
  ResourceInfoList = 'E_ResourceInfoList_TIMEOUT',
  PageTurning = 'E_PageTurning_TIMEOUT',
  ReadyTimeout = 'E_READY_TIMEOUT',
  JsonRequestError = 'E_JSON_REQUEST_ERROR',
}

export enum ItsEvent {
  Error = 'error', // { code: ItsError, msg: string }
  LoadingProgress = 'loadingProgress', // { total: number, loaded: number }
  StatusChange = 'statusChange', // { status: ItsStatus }
  CatalogueChange = 'catalogueChange', // { pages: ItsPage[] }
  CurrentPageChange = 'currentPageChange', // { oldId: string, newId: string }
  CoursewareLoad = 'coursewareLoad', // { url: string, loadTimes: number, urlType: number, status: number }
  MediaPlay = 'mediaPlay', // { url: string, loadTimes: number, urlType: number, status: number, type: string }
  MediaStatusChanged = 'mediaStatusChanged', // VideoStateChangedParam
  PageChanged = 'pageChanged',
  transferMessage = 'transferMessage',
  PageChangeToTeacher = 'pageChangeToTeacher',
  SendTestResult = 'sendTestResult',
  PageHasGame = 'pageHasGame',

  AnimationChange = 'animationChange',
  NeedChangePage = 'needChangePage',
  PageRestoredCompleted = 'pageRestoredCompleted',
  CoursewareHead = 'coursewareHead', // 课件头部尽头
  CoursewareTail = 'coursewareTail', // 课件尾部尽头
  PageRendered = 'pageRendered', // 页面渲染完成的事件上报
  GameAuthorizeSuccess = 'gameAuthorizeSuccess', // 游戏授权成功
  CancelAuthorizeSuccess = 'cancelAuthorizeSuccess', // 取消授权成功
  AnswerSyncSend = 'answer_sync_send', // 游戏单次作答上报
  GameReady = 'game_ready', // 游戏准备完成数据上报
  GameOver = 'game_over', // 游戏作答完成数据上报
  GameMsg = 'game_msg', // 所有来自游戏的SDK直接透传的消息
  /** 上报SDK初始化进度 */
  SDKInitProgress = 'SDKInitProgress',

  Console = 'console', // 控制台日志上报
  Statistic = 'statistic', // 统计数据上报
}

export enum ItsStatus {
  Created = 'created', // 播放器创建完成，可调用初始化
  Loading = 'loading', // 开始加载课件
  Loaded = 'loaded', // 课件加载完成
  Ready = 'ready', // 准备显示当前页
}

export enum ItsEnv {
  Prerelease = 'prerelease', // 仿真环境
  Production = 'production', // 生产环境
}
export type PageResource = {
  gameCoursewareId: number;
  gameEngineId: string;
  gameId: number;
  gameIsExtendRes: number;
  gameIsNewPlatform: number;
  gameIsPlay: number;
  gameIsPreReload: number;
  gameLocalPath: string;
  gameQuestionCount: number;
  gameTitleId: number;
  iframe: number;
  imgUrl: string;
  imgUrlSpareList: string[];
  isHide: number;
  pageConfig: null;
  pageId: string;
  questionType: number;
  resType: number;
};
export type CWInitParams = {
  url?: string;
  root?: HTMLElement;
  width: number;
  height: number;
  role: Role;
  itsId: string;
  showPagePercent: number;
  optimizationPageLoad: boolean;
  gameToast: string;
  gameTip: string;
  tipGameStart: string;
  tipGameEnd: string;
  bgColor: string;
  loadGame: string;
  gamePreload: string;
  pageResourceList?: PageResource[];
  rate?: string;
  log: (data, level?) => void;
  onPageChange: (oldPageId, currentPageId) => void;
  onPageChangeToTeacher: (data: unknown) => void;
  onSendTestResult: (data: unknown) => void;
  onCatelogChange: (catelogInfo: ItsPage[]) => void;
  onLoadError: (error: unknown) => void;
  onLoaded: () => void;
  onReady: () => void;
  onCWSendMessage?: (data: unknown) => void;
  storeCWData: (data: StoreCWStateParam) => Promise<boolean>;
  getCWGetData: () => unknown;
  onCWLoadingProgress?: (data: unknown) => void;
  onCWCheckResource: (data: CheckResourceAvailableParam) => void;
  onCWSendNeedChange?: (dir: NeedPageChangeDir) => void;
  onMediaStateChange?: (data: VideoStateChangedParam) => void;
};

export type FnCheckResource = (
  data: string[],
  callback: (info: Array<{ path: string; exist: boolean }>) => void
) => void;

export type FnDnsResolve = (
  host: string[],
  callback: (info: Array<{ host: string; resolved: string }>) => void
) => void;

export interface InitParam {
  gameToast: string;
  gameTip: string;
  screenWidth: number; // 屏幕宽度
  screenHeight: number; // 屏幕高度
  role: Role; // 角色，'teacher', 'student'
  itsId: string; // 课件ID
  // 为了计算远程资源，无论何时都需要remoteUrl，localUrl不为空则加载localUrl，否则加载remoteUrl
  localUrl: string;
  remoteUrl: string[];
  schoolCode?: string; // 分校code
  classId?: string; // 直播讲ID
  userId?: string; // 用户ID
  userName?: string; // 用户名，方便定位问题
  client?: string; // 授课端：tpc；PC端：Mac Windows；ios端：iPad iPhone；Android端：pad aphone
  bgColor?: string; // 背景颜色
  // timeout?: number;
  showPagePercent?: number;
  optimizationPageLoad?: boolean;
  getLatestItsMessage: (callback: (msg: ItsMessage) => void) => void; // 获取最后广播的Its消息
  // ['path1', 'path2'] -> [{ path: 'path1', exist: true }]
  checkResource: FnCheckResource;
  // 存储数据，如 { 'page1': {} },需要教师端实现
  storeData: (
    key: string,
    data: unknown,
    callback: (success: boolean) => void
  ) => void;
  getAllStoredData: (callback: (data: unknown) => void) => void; // 获取合并后所有的存储数据，如 { 'page1': {}, 'page2': {} }
  sendRoomItsMessage: (data: ItsMessage) => void; // 教师端实现，广播课件消息给其他端
  // writeLog: (level: string, msg: string) => void; // level: 'info', 'warn', 'error', 日志打印接口
  onEvent: (event: ItsEvent, data: unknown) => void; // 内部状态变化通知
  startPageId?: string; // 起始页码ID,页码恢复时优先选取该页码进行恢复，不存在时取服务器存储数据恢复；
  reportLogLevel?: number; // 日志上报级别,小于等于指定级别的日志全部上报阿里云,日志等级LogLevel，默认3
  isStepAutoPage?: boolean;
  // ['path1', 'path2'] -> [{ path: 'path1', exist: true }]
  isChangeAnimateStatus?: boolean; // 是否设置翻页动画重置
  /** 端上不需要的its事件类型列表，表中的列出的事件将不会被传到端上，若未传入列表，所有事件都将被传递 */
  blockItsEvents?: string[];
  clientDescription?: string; // 一些版本号或者其他信息 '{"version":"1.2.3"}'
  getStoredDataByKey?: (key: string, callback: (data: unknown) => void) => void;
  // setDataPoint?: (data: PointData) => void; // 打点数据上报
  // setStuPcLog?: PcStuLogStoreFn; // 学生端&PC端日志上报方法
  gamePreload?: string; // 是否开启交互游戏预加载
  loadGame?: string; // 内容云是否加载游戏
  pageResourceList?: PageResource[];
  tipGameStart?: string; // 游戏开始提示
  tipGameEnd?: string; // 游戏结束提示
  env?: string; // 游戏环境 test/online
  rate?: string; // 课件比例
}

export interface IMccPlayer {
  init(data: InitParam): void;
  handleRoomItsMessage(msg: ItsMessage): void; // 当前只需学生端调用，pemole发过来
  handleReconnected(): void; // 外部重连后，调用，内部会自动走获取全量消息逻辑
  reloadIts(): void; // 对应加载报错后重试功能，内部会按需切换CDN（如有），用作重新加载
  resizeCW(width: number, height: number): void;
}
export interface ITeacherMccPlayer extends IMccPlayer {
  pageDown(): void; // 下一页
  pageUp(): void; // 上一页
  pageTo(pageId: string): void; // 跳转到特定页
  // pageId 为空串 '' 表示第0页前插入, stay 是否停在当前页而不是跳到新增页
  insertPage(pageId: string, stay?: boolean): void;
  removePage(): void;
  animationDown(): void; // 下一个动画
  animationUp(): void; // 上一个动画
  setElementOperate(isDrag: boolean): void; // 设置元素可拖拽
}
