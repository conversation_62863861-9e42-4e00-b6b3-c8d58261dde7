function showGameDemoTip(isShow: boolean): void {
  if (isShow) {
    document
      .getElementById('gameDemoTip')
      .setAttribute('style', 'display:block');
  } else {
    document
      .getElementById('gameDemoTip')
      .setAttribute('style', 'display:none');
  }
}

function showGameStartTip(): void {
  document.getElementById('gameStartTip').classList.add('animation');
  document.getElementById('gameStartTip').setAttribute('style', 'display:flex');

  setTimeout(() => {
    document.getElementById('gameStartTip').classList.remove('animation');
    document
      .getElementById('gameStartTip')
      .setAttribute('style', 'display:none');
  }, 3200);
}
function showGameEndTip(): void {
  document.getElementById('gameEndTip').classList.add('animation');
  document.getElementById('gameEndTip').setAttribute('style', 'display:flex');

  setTimeout(() => {
    document.getElementById('gameEndTip').classList.remove('animation');
    document.getElementById('gameEndTip').setAttribute('style', 'display:none');
  }, 3200);
}
function showAnswerTip(isShow: boolean, text?: string): void {
  const dom = document.getElementById('answerTip');
  if (dom) {
    if (isShow) {
      dom.innerText = text;
      dom.setAttribute('style', 'display:block');
    } else {
      dom.setAttribute('style', 'display:none');
      dom.innerText = '';
    }
  } else {
    console.error('answerTip not found');
  }
}
export { showGameDemoTip, showGameStartTip, showGameEndTip, showAnswerTip };
