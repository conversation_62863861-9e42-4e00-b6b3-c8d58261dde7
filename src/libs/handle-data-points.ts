import { MediaLoadReportParam } from 'src/components/course-bridge';
import { ItsEvent } from '../interface';

// 加载结果
export enum LoadResult {
  succ = 1,
  err = 2,
}

// 加载地址
export enum LoadUrl {
  local = 1, // 本地课件
  remote = 2, // 远程课件
}
export type ItsDataPoints = {
  url: string; // 课件或多媒体的url
  loadTimes: number; // 课件加载时长/多媒体加载时长
  urlType: number; // 资源类型。1，本地课件；2，远程课件
  status: number; // 加载结果。1，加载成功；2，加载失败
  type: string; // 资源类型。courseware，课件；video，视频；audio，音频
  pageId: string;
  coursewareId: string;
};
// 数据类型
export enum LoadType {
  video = 'video',
  audio = 'audio',
  courseware = 'courseware',
}

class HandleLoad {
  private _loadTime: number = null;
  private _loadUrl: string;
  private _loadType: LoadUrl;
  private _reportHandle: (event: ItsEvent, data: unknown) => void;
  constructor(private _type: LoadType, reportHandle) {
    console.info(`[桥接层] HandleLoad ${_type} ${reportHandle}`);
    this._reportHandle = reportHandle;
  }

  startLoad(url: string, isLocal: boolean) {
    console.info(`[桥接层] startLoad ${url} ${isLocal} ${this._loadUrl}`);
    if (this._loadUrl === url) {
      console.info('[桥接层] startLoad url相同 retrun');
      return;
    }

    this._loadTime = Date.now();

    this._loadUrl = url;

    this._loadType = isLocal ? LoadUrl.local : LoadUrl.remote;
  }
  endLoad(isError = false) {
    console.info(`[桥接层] endLoad`, isError);
    if (this._loadTime > 0) {
      const params = {
        url: this._loadUrl,
        loadTimes: Date.now() - this._loadTime,
        urlType: this._loadType,
        status: isError ? LoadResult.err : LoadResult.succ,
        type: this._type,
      };
      console.info(`[桥接层] endLoad`, params);
      this._reportHandle(ItsEvent.CoursewareLoad, params);
      this._loadTime = null;
      this._loadUrl = null;
    }
  }
}

class HandleDataPoints {
  private _handleCourseware: HandleLoad;
  private coursewareId: string;
  private pageId: string;

  private reportHandler: (event: ItsEvent, data) => void;
  constructor() {
    this.coursewareId = '';
    this.pageId = '';
    this._handleCourseware = new HandleLoad(
      LoadType.courseware,
      (event: ItsEvent, data) => {
        if (this.reportHandler) {
          this.reportHandler(event, data);
        }
      }
    );
  }
  setCWId(coursewareId) {
    this.coursewareId = coursewareId;
  }
  setPageId(pageId: string) {
    this.pageId = pageId;
  }

  setReportHandler(reportHandler) {
    console.info(`[桥接层] setReportHandler ${reportHandler}`);
    this.reportHandler = (event: ItsEvent, data: ItsDataPoints) => {
      const params = {
        ...data,
        coursewareId: this.coursewareId,
        pageId: this.pageId,
      };
      reportHandler(event, params);
    };
  }
  handleCWReport(data: MediaLoadReportParam) {
    console.info(`[桥接层] handleCWReport ${data}`);
    const urlType = data.type;
    const params = { ...data, urlType };
    this.reportHandler(ItsEvent.MediaPlay, params);
  }

  startLoadCourseware(url: string, isLocal: boolean) {
    console.info(`[桥接层] startLoadCourseware ${url}`);

    this._handleCourseware.startLoad(url, isLocal);
  }

  endLoadCourseware(isError = false) {
    console.info(`[桥接层] startLoadCourseware ${isError}`);

    this._handleCourseware.endLoad(isError);
  }
}

const handleDataPoints = new HandleDataPoints();

export default handleDataPoints;
