// 获取URL参数
// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
function getUrlKey(name, url = window.location.href) {
  return (
    decodeURIComponent(
      // eslint-disable-next-line no-sparse-arrays
      (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(url) || [
        ,
        '',
      ])[1].replace(/\+/g, '%20')
    ) || null
  );
}

function isIos(): boolean {
  const UA = window.navigator.userAgent.toLowerCase();
  const isIOS = UA && /iphone|ipad|ipod|ios/.test(UA);
  return isIOS;
}
function isAndroid(): boolean {
  const UA = window.navigator.userAgent.toLowerCase();
  return UA && UA.indexOf('android') > 0;
}
function isPC(): boolean {
  if (
    navigator.userAgent.match(/Android/i) ||
    navigator.userAgent.match(/Mobile/i) ||
    navigator.userAgent.match(/HWCoursewareWeb_iOS/i)
  ) {
    return false;
  }
  return true;
}
function isThinkAcademyPC(): boolean {
  const UA = window.navigator.userAgent;
  if (/ThinkAcademy/i.test(UA) || /ThinkTeach/i.test(UA)) {
    return true;
  }
  return false;
}
function isArray(value: unknown): boolean {
  return Array.isArray(value);
}
function insertScript(url: string, onload: () => void): void {
  const script = document.createElement('script');
  document.body.appendChild(script);
  script.onload = () => {
    script.parentNode.removeChild(script);
    onload && onload();
  };
  script.src = url;
}
function padZero(num: unknown): string {
  return num >= 0 && num < 10 ? `0${num}` : String(num);
}
function formatTime(formats = 'Y-M-D', timestamp = ''): string {
  let ret;
  const myDate = timestamp ? new Date(timestamp) : new Date();
  const opt = {
    'Y+': myDate.getFullYear().toString(), // 年
    'M+': padZero(myDate.getMonth() + 1).toString(), // 月
    'D+': padZero(myDate.getDate()).toString(), // 日
    'H+': padZero(myDate.getHours()).toString(), // 时
    'm+': padZero(myDate.getMinutes()).toString(), // 分
    's+': padZero(myDate.getSeconds()).toString(), // 秒
    'S+': myDate.getMilliseconds().toString(), // 毫秒
  };
  for (const k in opt) {
    ret = new RegExp('(' + k + ')').exec(formats);
    if (ret) {
      formats = formats.replace(ret[1], opt[k]);
    }
  }
  return formats;
}
export {
  getUrlKey,
  isIos,
  isAndroid,
  insertScript,
  formatTime,
  isPC,
  isThinkAcademyPC,
  isArray,
};
