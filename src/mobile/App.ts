import { InitParam, Role, ItsEvent } from '../interface';
import './index.css';
import { getUrlKey } from '../libs/utils';
import StudentMccPlayer from '../components/player/student-mcc-player';
import TeacherMccPlayer from '../components/player/teacher-mcc-player';
import MccPlayerFactory from '../courseware-player';
import ClientBridge from './bridge';
import { version } from '../../package.json';

interface MobileInitParam extends InitParam {
  getInitParam?: GetInitParamFn;
}

interface ConsoleData {
  type: string;
  level: string;
  key: string;
  params: any;
}

type GetInitParamFn = (
  callback: (param: {
    itsId: string;
    localUrl: string;
    remoteUrl: string[];
  }) => void
) => void;
declare global {
  interface Window {
    eruda: {
      init: () => void;
    };
  }
}
export default class App extends ClientBridge {
  private params: MobileInitParam;
  private root: string | HTMLElement;
  private service: StudentMccPlayer | TeacherMccPlayer;
  private MessageTOClient;
  private currentPageId: string;
  constructor(el: string | HTMLElement) {
    super();
    this.params = {
      role: Role.Student,
      screenWidth: document.body.offsetWidth, // 屏幕宽度
      screenHeight: document.body.offsetHeight, // 屏幕高度
      itsId: '',
      localUrl: '',
      showPagePercent: 30,
      gameToast: '',
      gameTip: '',
      remoteUrl: [
        // 'https://kjdsfz-cdn.jiaoyanyun.com/webkjdsfiles/ed058c2488364d9ab57f55cbaec910a5/index.html?id=44f1d5c7617544b2beba2829c7249740&line=off&pageCount=4&devHideToolPanel=true',
        // 'https://kjdsfz-cdn.jiaoyanyun.com/webkjdsfiles/ed058c2488364d9ab57f55cbaec910a5/index.html?id=44f1d5c7617544b2beba2829c7249740&line=off&pageCount=4&devHideToolPanel=true',
        // 'https://kjds-cdn.speiyou.com/NRY/webkjdsfiles/2ca7891566a1483e802247f3bde379e7/index.html?id=8bff7aecdb084ad9886f761870217803&line=off&pageCount=60&env=31',
        // 'http://kjdsfz-cdn.jiaoyanyun.com/webkjdsfiles/7f90ec607ab448ea870717daa9a86b58/index.html?id=ff8d7b3f8fa344d2a685ad10a7d97fb2&line=off&pageCount=3',
        // 'https://kjdsfz-cdn.thethinkacademy.com/NRY/webkjdsfiles/ad9cac99385f4e61a97bf2ae72de80f0/index.html?id=0ca6e055fa30432c8fb1404329fb8d46&line=off&pageCount=36&env=31',
        // 'https://kjds-cdn.speiyou.com/NRY/webkjdsfiles/ccc8d7ed15d4476ca873076c484c8857123/index.html?id=7845516c332243dca51ac6338bf00dc91&line=off&pageCount=35&env=31&devShowToolPanel=true',
      ],
      bgColor: 'transparent',
      pageResourceList: [],
      getLatestItsMessage: async (cb) => {
        let data;
        try {
          data = JSON.parse(data);
        } catch (e) {
          console.warn('error latest message', data);
        }
        cb(data);
      },
      checkResource: (data, callback) => {
        callback(
          data.map((item) => {
            return { path: item, exist: false };
          })
        );
      },
      storeData: this.storeData.bind(this),
      getAllStoredData: this.getAllStoredData.bind(this),
      sendRoomItsMessage: this.sendRoomItsMessage.bind(this),
      onEvent: this.handleEvent.bind(this),
    };

    this.root = el;
    this.init();
  }
  private handleEvent(event: ItsEvent, data: any) {
    if (
      data.code === 'E_LOAD_TIMEOUT' ||
      data.code === 'E_LOAD_ERROR' ||
      event === ItsEvent.Error
    ) {
      return this.emitMessage('coursewareStatus', {
        status: 'error',
        total: 0,
        loaded: 0,
        errMsg: data.code || '',
        url: data.url,
        isLocal: data.isLocal,
      });
    }
    switch (event) {
      case ItsEvent.CurrentPageChange:
        this.currentPageId = (data as { [key: string]: string }).newId;
        window.currentPageId = this.currentPageId;
        break;
      case ItsEvent.PageChangeToTeacher:
        /*eslint no-case-declarations: "off"*/
        /*eslint-env es6*/
        if (this.params.role === Role.Student) {
          this.emitMessage('pageChanged', data);
        }
        const pageData = data.currentPageData || {};
        const questionData = data.questionData || '';
        const interactiveTemplatePath = data.interactiveTemplatePath || '';
        const includeInteractiveTemplate =
          data.includeInteractiveTemplate || false;
        if (interactiveTemplatePath && includeInteractiveTemplate) {
          let gameId = '';
          try {
            gameId = pageData.items[0].engineId;
          } catch (error) {
            console.warn(error);
          }
          const pageChangedData = {
            isGame: true,
            gameId, // 游戏ID
            pageId: pageData.id, // 当前页ID
            pageTitle: pageData.title, // 当前页标题
            interactiveTemplatePath, // 游戏地址
          };
          if (this.params.role === Role.Teacher) {
            this.emitMessage('pageChanged', pageChangedData);
          }
          return;
        }
        if (questionData && questionData.queId) {
          const questionOptionList = questionData.answerOptionList || '';
          const answerOptionList = [];
          if (questionOptionList) {
            const _optionList = questionOptionList.flat();
            _optionList.forEach((item) => {
              answerOptionList.push([item.aoVal]);
            });
          }
          const pageChangedData = {
            isGame: false,
            pageId: pageData.id, // 当前页ID
            pageTitle: pageData.title, // 当前页标题
            paperId: questionData.paperId, // 试卷ID
            quesId: questionData.queId, // 题目ID
            quesTypeId: questionData.logicQuesTypeId, // 题目类型ID
            quesTypeName: questionData.logicQuesTypeName, // 题目类型名称
            quesAnswer: questionData.answer, // 正确答案
            quesOptions: answerOptionList, // 选项列表
            quesScore: questionData.score, // 本题积分
            quesContent: questionData || null, // 这应该是题干
          };
          if (this.params.role === Role.Teacher) {
            this.emitMessage('pageChanged', pageChangedData);
          }
        }
        break;
      case ItsEvent.StatusChange:
        if (data.status === 'loaded') {
          this.MessageTOClient = {
            status: 'loaded',
            total: 0,
            loaded: 0,
            url: data.url,
            isLocal: data.isLocal,
          };
          this.emitMessage('coursewareStatus', this.MessageTOClient);
        }
        break;
      case ItsEvent.LoadingProgress:
        this.MessageTOClient = {
          status: 'loading',
          total: data.total,
          loaded: data.loaded,
        };
        this.emitMessage('coursewareStatus', this.MessageTOClient);
        break;
      case ItsEvent.PageHasGame:
        this.emitMessage(ItsEvent.PageHasGame, {});
        break;
      case ItsEvent.GameReady:
        this.emitMessage(ItsEvent.GameReady, data);
        break;
      case ItsEvent.GameOver:
        this.emitMessage(ItsEvent.GameOver, data);
        break;
      case ItsEvent.Console:
        this.handleConsole(data);
        break;
      case ItsEvent.Statistic:
        this.handleStatistic(data);
        break;
      case ItsEvent.SendTestResult:
        this.emitMessage(ItsEvent.SendTestResult, {
          result: data,
          interactId: this.service.interactId,
        });
        break;
    }
  }

  private handleStatistic(data) {
    this.emitMessage('statistic', data);
  }
  private handleConsole(data) {
    this.emitMessage('console', {
      type: 'log',
      level: 'info',
      key: 'courseware',
      params: data,
    });
  }
  private storeData(key, data, callback) {
    this.emitMessage('stateRecover', {
      [key]: data,
      //value: data,
    });
    callback(true);
  }
  private getAllStoredData(callback) {
    callback(this.allStoreMessages);
  }
  private init() {
    if (getUrlKey('itsId')) {
      this.initUrlParams();
      this.createService();
      console.log('window.JsInjectionActivity.coursewareinfo', '开始初始化了');
      try {
        const coursewareInfo = JSON.parse(
          window.JsInjectionActivity.jsCallJsonResult(
            JSON.stringify({
              className: 'HWClassCourseware',
              methodName: 'getCoursewareInfo',
            })
          )
        );
        this.params = Object.assign(this.params, coursewareInfo);
      } catch (error) {
        console.error('获取课件信息失败', error);
      }

      this.service.init(this.params);
      this.emitMessage('bridge_info', {
        version: version,
        canUseVote: true,
      });
    } else {
      // document.getElementById('app').innerHTML = 'itsId 不能为空';
      console.warn('itsId 不能为空');
    }
  }

  createService(): void {
    const { role = Role.Student } = this.params;
    this.service = MccPlayerFactory.getPlayer(role, this.root);
    super.initClientBridge(this.service, role);
  }

  // 发送数据
  sendRoomItsMessage(data: unknown): void {
    const { role = Role.Student } = this.params;
    if (role === Role.Teacher) {
      this.emitMessage('directForward', data);
    }
  }
  initUrlParams(): void {
    const handleUrlMap = [
      {
        key: 'itsId',
      },
      {
        key: 'localUrl',
      },
      {
        key: 'remoteUrl',
        handle: (val) => JSON.parse(val), // 从URL传入的remoteUrl需要解析为数组
      },
      {
        key: 'role',
      },
      {
        key: 'screenWidth',
      },
      {
        key: 'screenHeight',
      },
      {
        key: 'classId',
      },
      {
        key: 'userId',
      },
      {
        key: 'userName',
      },
      {
        key: 'client',
      },
      {
        key: 'showPagePercent',
      },
      {
        key: 'optimizationPageLoad',
      },
      {
        key: 'schoolCode',
      },
      {
        key: 'loadGame',
      },
      {
        key: 'gamePreload',
      },
      // {
      //   key: 'gameToast',
      // },
      // {
      //   key: 'gameTip',
      // },
    ];
    // 给this.params赋值
    handleUrlMap.forEach((item) => {
      const { key, handle } = item;
      const sVal = getUrlKey(key);
      if (sVal) {
        this.params[key] = handle ? handle(sVal) : sVal;
      }
    });
  }
  getCurrentPageId(): string {
    return this.currentPageId;
  }
}
