import { Role, ItsPageType } from '../interface';
import { formatTime } from '../libs/utils';

declare global {
  interface Window {
    webkit: any;
    JsInjectionActivity: any;
    currentPageId: string;
    pageDown: () => void;
    pageUp: () => void;
    pageTo: (pageId: string) => void;
    insertPage: () => void;
    removePage: () => void;
    animationDown: () => void;
    handleItsMessage: (msg: any) => void;
    getAllItsMessage: (msg: any) => void;
    handleMessageToCourseware: (cwAction: any) => void;
  }
}

export default class ClientBridge {
  private static __service;
  private static __role;
  public allStoreMessages;
  initClientBridge(service: unknown, role: unknown): void {
    ClientBridge.__service = service;
    ClientBridge.__role = role;
    if (ClientBridge.__role === Role.Teacher) {
      window.JsInjectionActivity = {};
      window.JsInjectionActivity.jsCallClient = function (data: unknown) {
        window.webkit.messageHandlers.HWClassCourseware.postMessage(data);
      };
      this.onClientMessage();
    } else {
      window.handleItsMessage = (msg) => {
        if (ClientBridge.__role === Role.Student) {
          ClientBridge.__service.handleRoomItsMessage(msg);
        }
      };
      window.handleMessageToCourseware = (msg) => {
        ClientBridge.__service.handleMessageToCourseware(msg);
      };
    }
  }
  private onClientMessage(): void {
    // 下翻页
    window.pageDown = () => {
      ClientBridge.__service.pageDown();
    };
    // 上翻页
    window.pageUp = () => {
      ClientBridge.__service.pageUp();
    };
    // 跳转到某页
    window.pageTo = (pageId) => {
      ClientBridge.__service.pageTo(pageId);
    };
    // 下一步
    window.animationDown = () => {
      ClientBridge.__service.animationDown();
    };
    window.insertPage = (imgUrl = '', isStay = false) => {
      ClientBridge.__service.insertPage(
        window.currentPageId,
        isStay,
        imgUrl,
        ItsPageType.Blank
      );
    };
    window.removePage = () => {
      ClientBridge.__service.removePage();
    };
    // 获取异常恢复信息
    window.getAllItsMessage = (msg) => {
      this.allStoreMessages = typeof msg === 'string' ? msg : JSON.parse(msg);
    };
  }
  emitMessage(methodName: string, params: unknown): void {
    const data = JSON.stringify({
      className: 'HWClassCourseware',
      methodName,
      params: params || {},
    });
    console.log(
      `%c[${formatTime('Y-M-D H:m:s:S')}]%c 来自JSBridge:`,
      `color: #049be3;`,
      '',
      JSON.parse(data)
    );
    process.env.NODE_ENV !== 'development' &&
      window.JsInjectionActivity.jsCallClient(data);
  }
}
