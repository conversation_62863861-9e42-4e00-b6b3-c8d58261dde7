/* @font-face {
  font-family: SFMedium;
  src: url('./SF-Pro-Rounded-Medium.otf');
}*/
html,
body {
  overflow: hidden;
  width: 100%;
  height: 100%;
  font-size: 16px;
  padding: 0;
  margin: 0;
  background: transparent;
  font-family: 'SFProRounded-Medium', sans-serif, system-ui;
  /* font-family: SFMedium, sans-serif, system-ui; */
}
* {
  box-sizing: border-box;
}
#app {
  position: relative;
  width: 100%;
  height: 100%;
}
p {
  padding: 0;
  margin: 0;
}
.hideLoading {
  display: none;
  opacity: 0;
}
.loading,
.ne-dialog {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  background: url(./wait-bg.jpg) no-repeat center center;
  background-size: 100% 100%;
}
.loading-contenter {
  position: absolute;
  background: rgba(255, 255, 255, 1);
  border-radius: 100%;
  width: 80px;
  height: 80px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.loading-logo {
  position: absolute;
  width: 50px;
  height: 50px;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: url('./logo-loading.png') no-repeat center center;
  background-size: 100% 100%;
}
.loading-animation,
.loading-animation:after {
  border-radius: 50%;
  width: 82px;
  height: 82px;
  margin: -1px 0 0 -1px;
  box-sizing: border-box;
}
.loading-animation {
  font-size: 10px;
  position: relative;
  text-indent: -9999em;
  border-top: 6px solid rgba(255, 182, 15, 0.5);
  border-right: 6px solid rgba(255, 182, 15, 0.5);
  border-bottom: 6px solid rgba(255, 182, 15, 0.5);
  border-left: 6px solid #ffb60f;
  transform: translateZ(0);
  animation: load 1s infinite linear;
}
@keyframes load {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

.ne-dialog {
  display: none;
}
.ne-dialog-contenter {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #fff;
  border-radius: 16px;
  min-width: 360px;
  min-height: 200px;
  text-align: center;
}
.ne-dialog--body {
  padding: 30px 10px 10px 20px;
  font-size: 20px;
  font-family: Helvetica, PingFang SC, 'sans-serif', Arial, Verdana,
    Microsoft YaHei;
  color: rgba(23, 43, 77, 1);
}
.ne-dialog--body h3 {
  font-size: 20px;
  padding: 10px 0 10px 0;
  margin: 0;
}
.ne-dialog--body p {
  line-height: 20px;
  font-size: 16px;
  color: #a2aab8;
}
.ne-dialog--footer {
  position: absolute;
  bottom: 16px;
  display: flex;
  justify-content: space-around;
  left: 16px;
  right: 16px;
}
.button {
  background: linear-gradient(
    45deg,
    rgba(255, 213, 24, 1) 0%,
    rgba(255, 170, 10, 1) 100%
  );
  border-radius: 30px;
  font-size: 16px;
  font-weight: 600;
  color: rgba(255, 255, 255, 1);
  line-height: 20px;
  border: 0 none;
  padding: 15px 0px;
  width: 45%;
  outline: 0 none;
  text-align: center;
  cursor: pointer;
}
.button.is-cancel {
  background: rgba(255, 243, 220, 1);
  color: #ffaa0a;
}
.gameDemoTip {
  background: rgba(0, 0, 0, 0.8);
  position: absolute;
  bottom: 10px;
  left: 10px;
  z-index: 100;
  min-height: 30px;
  padding: 15px 15px 15px 30px;
  border-radius: 10px;
  color: #fff;
  justify-content: center;
  align-items: center;
  text-align: center;
  line-height: 20px;
  font-size: 16px;
  font-weight: 500;
  display: none;
  letter-spacing: 1px;
}
.gameDemoTip::before {
  position: absolute;
  left: 10px;
  top: 50%;
  width: 10px;
  height: 10px;
  background: #3370ff;
  content: '';
  transform: translate(0, -50%);
  border-radius: 10px;
}
.answerTip {
  background: rgba(15, 25, 42, 0.7);
  border-radius: 8px;
  backdrop-filter: blur(4px);
  position: absolute;
  bottom: 16px;
  left: 16px;
  z-index: 100;
  padding: 12px 10px 12px 30px;
  color: #fff;
  line-height: 18px;
  font-size: 16px;
  font-weight: 600;
  display: none;
}
.answerTip::before {
  background: url('./answerTip/right.png') no-repeat center center;
  background-size: 16px 16px;
  width: 16px;
  height: 16px;
  position: absolute;
  left: 10px;
  top: 50%;
  content: '';
  transform: translate(0, -50%);
}
.gameDemoToast {
  background: rgba(0, 0, 0, 0.95);
  position: absolute;
  max-width: 275px;
  top: 50%;
  left: 50%;
  z-index: 100;
  min-height: 30px;
  padding: 10px;
  border-radius: 10px;
  color: #fff;
  justify-content: center;
  align-items: center;
  text-align: center;
  line-height: 20px;
  font-size: 16px;
  transform: translate(-50%, -50%);
  font-weight: 500;
  display: none;
}
.gameEndTip,
.gameStartTip {
  width: 100%;
  height: 100%;
  z-index: 100;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0);
  animation: bgFadeIn 200ms ease-out forwards;
  display: flex;
  justify-content: center;
}
.banner {
  width: 100%;
  height: 17.6%;
  position: absolute;
  bottom: 35.1%;
  background: url('./gameStart/banner.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  opacity: 0;
  align-items: center;
  justify-content: center;
  animation-duration: 200ms;
}
.banner .text {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 44px;
  font-weight: 900;
  color: #ae4d00;
  animation-duration: 200ms;
  opacity: 0;
}
.banner .sunline1 {
  width: 20.6%;
  height: 100%;
  position: absolute;
  background: url('./gameStart/sunline1.png') no-repeat center center;
  background-size: 100% 100%;
  left: 0;
  opacity: 0;
  animation-duration: 200ms;
}
.banner .sunline2 {
  background: url('./gameStart/sunline2.png') no-repeat center center;
  background-size: 100% 100%;
  width: 15%;
  height: 100%;
  position: absolute;
  right: 0;
  opacity: 0;
  animation-duration: 200ms;
}
.streamer {
  width: 71%;
  height: 50%;
  position: absolute;
  bottom: 39%;
  opacity: 0;
  transform: scale(0.4);
  animation-duration: 200ms;
  background: url('./gameStart/streamer.png') no-repeat center center;
  background-size: 100% 100%;
}
.up {
  background: url('./gameStart/up.png') no-repeat center center;
  background-size: 100% 100%;
  width: 61%;
  height: 40%;
  position: absolute;
  z-index: -1;
  bottom: 39%;
  text-align: center;
  opacity: 0;
  transform: scale(0.7);
  animation-delay: 200ms;
}
.up.rate2 {
  height: 50%;
}
.gameEndTip .up {
  background: url('./gameEnd/up.png') no-repeat center center;
  background-size: 100% 100%;
}
.animation .banner {
  animation: bannerFadeIn 3s ease-in-out forwards;
}
.animation .banner .text {
  animation: textAnimation 3s ease-in-out forwards;
}
.animation .banner .sunline1 {
  animation: sunline1 3s linear infinite;
}
.animation .banner .sunline2 {
  animation: sunline2 3s linear infinite;
}
.animation .up {
  animation: upAnimation 3s ease-in-out forwards;
}
.animation .streamer {
  animation: streamerAnimation 3s ease-in-out forwards;
}

@keyframes upAnimation {
  0% {
    transform: scale(0.7);
    opacity: 0;
  }
  20% {
    transform: scale(0.7);
    opacity: 0;
  }
  30% {
    transform: scale(1.06);
    opacity: 1;
  }
  35% {
    transform: scale(0.9);
    opacity: 1;
  }
  38% {
    transform: scale(1.05);
    opacity: 1;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
  90% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes bgFadeIn {
  to {
    background: rgba(0, 0, 0, 0.4);
  }
}

@keyframes bannerFadeIn {
  0% {
    opacity: 0;
  }
  10% {
    opacity: 1;
  } /* 200ms / 3s = 6.67%，四舍五入到 10% */
  90% {
    opacity: 1;
  } /* 等待 2.6s */
  100% {
    opacity: 0;
  } /* 最后 200ms 将透明度从 1 变为 0 */
}
@keyframes textAnimation {
  0% {
    transform: scale(3);
    opacity: 0;
  }
  10% {
    transform: scale(3);
    opacity: 0;
  }
  20% {
    transform: scale(0.9);
    opacity: 1;
  }

  25% {
    transform: scale(1.05);
    opacity: 1;
  }
  30% {
    transform: scale(1);
    opacity: 1;
  }
  80% {
    transform: scale(1);
    opacity: 1;
  }
  90% {
    transform: scale(3);
    opacity: 0;
  }
}

@keyframes sunline1 {
  33% {
    left: 0;
    opacity: 0;
  }
  40% {
    opacity: 1;
  }
  60% {
    opacity: 1;
  }
  66% {
    left: 100%;
    opacity: 0;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}
@keyframes sunline2 {
  40% {
    left: 0;
    opacity: 0;
  }
  57% {
    opacity: 1;
  }
  77% {
    opacity: 1;
  }
  83% {
    left: 100%;
    opacity: 0;
  }
  100% {
    left: 100%;
    opacity: 0;
  }
}
@keyframes streamerAnimation {
  0% {
    transform: scale(0.4);
    opacity: 0;
  }
  20% {
    transform: scale(0.4);
    opacity: 0;
  }
  30% {
    transform: scale(1);
    opacity: 1;
  }
  80% {
    transform: scale(1);
    opacity: 1;
  }
  90% {
    transform: scale(1);
    opacity: 0;
  }
}
