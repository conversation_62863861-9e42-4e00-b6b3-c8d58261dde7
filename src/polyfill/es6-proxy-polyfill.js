(function (root, factory) {
  if (!root.Proxy) {
    root.Proxy = factory();
  }
  // eslint-disable-next-line no-undef
})(window, function () {
  function createProxy(target, handler) {
    if (typeof target !== 'object' || typeof handler !== 'object') {
      console.error('[proxy-polyfill] Proxy format does not match');
      return;
    }

    const { set, get } = handler;

    const targetCopy = Object.assign(Object.create(target), target);

    Object.keys(targetCopy).forEach((key) => {
      const oDefineProperty = {};

      oDefineProperty['set'] = set
        ? (val) => handler.set(target, key, val)
        : (val) => (target[key] = val);

      oDefineProperty['get'] = get
        ? () => handler.get(target, key)
        : () => target[key];

      Object.defineProperty(targetCopy, key, oDefineProperty);
    });

    return targetCopy;
  }

  return createProxy;
});
