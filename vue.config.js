const path = require('path');
const resolve = (dir) => path.join(__dirname, dir);
const isProd = process.env.NODE_ENV === 'production';
const config = require('./build/config');
const envs = require('./build/envs');

const env = process.env.SCRIPT;
const envConfig = config[env];

module.exports = {
  devServer: {
    // port: envConfig.port,
    overlay: {
      warnings: false,
      errors: false,
    },
  },
  publicPath: './',
  assetsDir: 'static',
  pages: {
    index: {
      entry: envConfig.entry,
      template: 'public/index.html',
      filename: 'index.html',
    },
  },
  productionSourceMap: false,
  lintOnSave: false,
  outputDir: envConfig.outputDir,
  configureWebpack: {
    entry: envConfig.componentEntry || { index: envConfig.entry },
    output: {
      filename: 'static/js/[name].[hash:8].js',
      chunkFilename: 'static/js/[name].[chunkhash:8].js',
      libraryTarget: envConfig.libraryTarget || 'umd',
    },
    // output: {
    //   filename: '[hash].js',
    //   libraryTarget: envConfig.libraryTarget || 'umd'
    // },
    resolve: {
      extensions: ['.ts', '.tsx', '.js', '.json', '.vue'],
    },
    module: {
      rules: [
        {
          test: /\.tsx?$/,
          loader: 'ts-loader',
          exclude: /node_modules/,
          options: {
            appendTsSuffixTo: [/\.vue$/],
          },
        },
      ],
    },
    // plugins,
  },
  chainWebpack: (config) => {
    config.resolve.alias.set('@', resolve('src'));
    config.module
      .rule('images')
      .use('url-loader')
      .loader('url-loader')
      .tap((options) =>
        Object.assign(options, {
          limit: 1000000,
        })
      );
    if (env === envs.PROD_PC) {
      config.optimization.delete('splitChunks');
      config.plugins.delete('copy');
      config.plugins.delete('html');
      config.plugins.delete('preload');
      config.plugins.delete('prefetch');
      config.plugins.delete('hmr');
      config.entryPoints.delete('app');
    }
  },
  css: {
    sourceMap: !isProd,
    extract: {
      filename: 'static/css/courseware.css', //在lib文件夹中建立style文件夹中，生成对应的css文件。
    },
  },
};
